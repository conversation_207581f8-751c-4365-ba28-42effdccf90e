"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { useAuth } from "@/lib/auth-context";
import {
  Settings,
  Shield,
  Bell,
  Database,
  Key,
  Trash2,
  AlertTriangle,
} from "lucide-react";

function SettingsContent() {
  const { user, userProfile } = useAuth();
  const [notifications, setNotifications] = useState({
    email: true,
    webhook: false,
    errors: true,
  });

  const handleNotificationChange = (type: keyof typeof notifications) => {
    setNotifications((prev) => ({
      ...prev,
      [type]: !prev[type],
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
              <p className="text-gray-600 mt-1">
                Manage your application preferences and account settings
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-8">
          {/* General Settings */}
          <div className="content-card">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Settings className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  General Settings
                </h2>
                <p className="text-gray-600">
                  Manage your general application preferences
                </p>
              </div>
            </div>
            <div className="space-y-6">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    Dark Mode
                  </Label>
                  <p className="text-sm text-gray-500">
                    Toggle between light and dark themes
                  </p>
                </div>
                <Switch />
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    Auto-save Configurations
                  </Label>
                  <p className="text-sm text-gray-500">
                    Automatically save service configurations
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="content-card">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <Bell className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Notifications
                </h2>
                <p className="text-gray-600">
                  Configure how you receive notifications
                </p>
              </div>
            </div>
            <div className="space-y-6">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    Email Notifications
                  </Label>
                  <p className="text-sm text-gray-500">
                    Receive notifications via email
                  </p>
                </div>
                <Switch
                  checked={notifications.email}
                  onCheckedChange={() => handleNotificationChange("email")}
                />
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    Webhook Notifications
                  </Label>
                  <p className="text-sm text-gray-500">
                    Send notifications to external webhooks
                  </p>
                </div>
                <Switch
                  checked={notifications.webhook}
                  onCheckedChange={() => handleNotificationChange("webhook")}
                />
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    Error Notifications
                  </Label>
                  <p className="text-sm text-gray-500">
                    Get notified when connections fail
                  </p>
                </div>
                <Switch
                  checked={notifications.errors}
                  onCheckedChange={() => handleNotificationChange("errors")}
                />
              </div>
            </div>
          </div>

          {/* Security Settings */}
          <div className="content-card">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Shield className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Security
                </h2>
                <p className="text-gray-600">
                  Manage your account security and access
                </p>
              </div>
            </div>
            <div className="space-y-6">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    Two-Factor Authentication
                  </Label>
                  <p className="text-sm text-gray-500">
                    Add an extra layer of security to your account
                  </p>
                </div>
                <Badge
                  variant="outline"
                  className="bg-yellow-50 text-yellow-700 border-yellow-200"
                >
                  Coming Soon
                </Badge>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    API Keys
                  </Label>
                  <p className="text-sm text-gray-500">
                    Manage API keys for external integrations
                  </p>
                </div>
                <button className="btn-primary text-sm px-4 py-2 flex items-center space-x-2">
                  <Key className="w-4 h-4" />
                  <span>Manage</span>
                </button>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    Session Management
                  </Label>
                  <p className="text-sm text-gray-500">
                    View and manage active sessions
                  </p>
                </div>
                <button className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-sm">
                  View Sessions
                </button>
              </div>
            </div>
          </div>

          {/* Data & Privacy */}
          <div className="content-card">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <Database className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Data & Privacy
                </h2>
                <p className="text-gray-600">
                  Control your data and privacy settings
                </p>
              </div>
            </div>
            <div className="space-y-6">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    Data Export
                  </Label>
                  <p className="text-sm text-gray-500">
                    Download all your data in JSON format
                  </p>
                </div>
                <button className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-sm">
                  Export Data
                </button>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    Analytics
                  </Label>
                  <p className="text-sm text-gray-500">
                    Help improve the service by sharing usage data
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
            </div>
          </div>

          {/* Danger Zone */}
          <div className="content-card border-red-200 bg-red-50">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-red-600">
                  Danger Zone
                </h2>
                <p className="text-red-600">
                  Irreversible and destructive actions
                </p>
              </div>
            </div>
            <div className="space-y-6">
              <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-red-200">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    Delete All Connections
                  </Label>
                  <p className="text-sm text-gray-500">
                    Permanently delete all service connections
                  </p>
                </div>
                <button className="px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700 text-sm flex items-center space-x-2">
                  <Trash2 className="w-4 h-4" />
                  <span>Delete All</span>
                </button>
              </div>

              <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-red-200">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium text-gray-900">
                    Delete Account
                  </Label>
                  <p className="text-sm text-gray-500">
                    Permanently delete your account and all data
                  </p>
                </div>
                <button className="px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700 text-sm flex items-center space-x-2">
                  <Trash2 className="w-4 h-4" />
                  <span>Delete Account</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function SettingsPage() {
  return (
    <ProtectedRoute>
      <SettingsContent />
    </ProtectedRoute>
  );
}
