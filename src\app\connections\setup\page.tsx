"use client";

import { useState, useEffect } from "react";
import { use<PERSON>earch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  MessageSquare,
  Phone,
  Mail,
  Slack,
  CheckCircle,
  AlertCircle,
  Loader2,
  Eye,
  EyeOff,
  Copy,
  ExternalLink,
  Plus,
} from "lucide-react";
import { ExternalService, ConnectionTestResult } from "@/types/n8n";

// This would normally come from your API or database
const serviceConfigs: Record<string, ExternalService> = {
  telegram: {
    id: "telegram",
    name: "Telegram",
    type: "telegram",
    icon: "MessageSquare",
    description: "Connect your Telegram bot to receive and send messages",
    authType: "api_key",
    configFields: [
      {
        name: "botToken",
        label: "Bot Token",
        type: "password",
        required: true,
        placeholder: "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz",
        description: "Get this from @BotFather on Telegram",
      },
      {
        name: "botName",
        label: "Bot Name",
        type: "text",
        required: true,
        placeholder: "My Awesome Bot",
        description: "A friendly name for your bot",
      },
    ],
    isConnected: false,
  },
  whatsapp: {
    id: "whatsapp",
    name: "WhatsApp Business",
    type: "whatsapp",
    icon: "Phone",
    description: "Connect WhatsApp Business API for messaging automation",
    authType: "bearer_token",
    configFields: [
      {
        name: "accessToken",
        label: "Access Token",
        type: "password",
        required: true,
        placeholder: "EAAxxxxxxxxxxxxxxx",
        description: "Get this from Facebook Developer Console",
      },
      {
        name: "phoneNumberId",
        label: "Phone Number ID",
        type: "text",
        required: true,
        placeholder: "1234567890123456",
        description: "Your WhatsApp Business phone number ID",
      },
      {
        name: "businessName",
        label: "Business Name",
        type: "text",
        required: true,
        placeholder: "My Business",
        description: "Name of your business",
      },
    ],
    isConnected: false,
  },
};

export default function ServiceSetupPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const serviceType = searchParams?.get("service");

  const [formData, setFormData] = useState<Record<string, string>>({});
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>(
    {}
  );
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<ConnectionTestResult | null>(
    null
  );
  const [isCreatingConnection, setIsCreatingConnection] = useState(false);
  const [webhookUrl, setWebhookUrl] = useState<string>("");

  const service = serviceType ? serviceConfigs[serviceType] : null;

  useEffect(() => {
    if (service) {
      // Initialize form data with empty values
      const initialData: Record<string, string> = {};
      service.configFields.forEach((field) => {
        initialData[field.name] = "";
      });
      setFormData(initialData);
    }
  }, [service]);

  const getServiceIcon = (iconName: string) => {
    const icons = {
      MessageSquare,
      Phone,
      Mail,
      Slack,
    };
    return icons[iconName as keyof typeof icons] || MessageSquare;
  };

  const handleInputChange = (fieldName: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [fieldName]: value,
    }));
  };

  const togglePasswordVisibility = (fieldName: string) => {
    setShowPasswords((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }));
  };

  const testConnection = async () => {
    if (!service) return;

    setIsTestingConnection(true);
    setTestResult(null);

    try {
      const response = await fetch(`/api/external-services/${service.type}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "test_connection",
          ...formData,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setTestResult(result.data);
      } else {
        setTestResult({
          success: false,
          message: result.error || "Connection test failed",
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: "Network error occurred",
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const createConnection = async () => {
    if (!service || !testResult?.success) return;

    setIsCreatingConnection(true);

    try {
      const response = await fetch(`/api/external-services/${service.type}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "create_webhook",
          [service.type === "telegram" ? "botName" : "businessName"]:
            formData[service.type === "telegram" ? "botName" : "businessName"],
          [service.type === "telegram" ? "botToken" : "config"]:
            service.type === "telegram" ? formData.botToken : formData,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Set webhook URL for display
        const webhookPath = result.data.webhookPath;
        setWebhookUrl(`${window.location.origin}/webhook/${webhookPath}`);

        // Redirect to connections page after a delay
        setTimeout(() => {
          router.push("/connections");
        }, 3000);
      } else {
        console.error("Failed to create connection:", result.error);
      }
    } catch (error) {
      console.error("Error creating connection:", error);
    } finally {
      setIsCreatingConnection(false);
    }
  };

  const copyWebhookUrl = () => {
    if (webhookUrl) {
      navigator.clipboard.writeText(webhookUrl);
    }
  };

  if (!service) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-4">
                <Link href="/connections">
                  <Button variant="ghost" size="icon">
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                </Link>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <Plus className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                    New Connection
                  </h1>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Service Selection */}
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Choose a Service to Connect
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Select the external service you want to integrate with your n8n
              workflows.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Object.values(serviceConfigs).map((serviceConfig) => {
              const IconComponent = getServiceIcon(serviceConfig.icon);
              return (
                <Card
                  key={serviceConfig.id}
                  className="hover:shadow-lg transition-shadow cursor-pointer"
                >
                  <Link
                    href={`/connections/setup?service=${serviceConfig.type}`}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                          <IconComponent className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {serviceConfig.name}
                          </h3>
                          <Badge variant="outline" className="mt-1">
                            {serviceConfig.authType.replace("_", " ")}
                          </Badge>
                        </div>
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        {serviceConfig.description}
                      </p>
                      <div className="mt-4 flex items-center text-blue-600 dark:text-blue-400 text-sm font-medium">
                        <span>Configure Service</span>
                        <ExternalLink className="ml-1 h-3 w-3" />
                      </div>
                    </CardContent>
                  </Link>
                </Card>
              );
            })}
          </div>
        </main>
      </div>
    );
  }

  const IconComponent = getServiceIcon(service.icon);
  const isFormValid = service.configFields
    .filter((field) => field.required)
    .every((field) => formData[field.name]?.trim());

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/connections">
                <Button variant="ghost" size="icon">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <IconComponent className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Connect {service.name}
                </h1>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Configuration Form */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Service Configuration</CardTitle>
                <CardDescription>{service.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {service.configFields.map((field) => (
                  <div key={field.name} className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {field.label}
                      {field.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </label>
                    <div className="relative">
                      <Input
                        type={
                          field.type === "password" &&
                          !showPasswords[field.name]
                            ? "password"
                            : "text"
                        }
                        placeholder={field.placeholder}
                        value={formData[field.name] || ""}
                        onChange={(e) =>
                          handleInputChange(field.name, e.target.value)
                        }
                        className={field.type === "password" ? "pr-10" : ""}
                      />
                      {field.type === "password" && (
                        <button
                          type="button"
                          onClick={() => togglePasswordVisibility(field.name)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2"
                        >
                          {showPasswords[field.name] ? (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          ) : (
                            <Eye className="h-4 w-4 text-gray-400" />
                          )}
                        </button>
                      )}
                    </div>
                    {field.description && (
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {field.description}
                      </p>
                    )}
                  </div>
                ))}

                <div className="pt-4 space-y-3">
                  <Button
                    onClick={testConnection}
                    disabled={!isFormValid || isTestingConnection}
                    className="w-full"
                    variant="outline"
                  >
                    {isTestingConnection ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Testing Connection...
                      </>
                    ) : (
                      "Test Connection"
                    )}
                  </Button>

                  {testResult?.success && (
                    <Button
                      onClick={createConnection}
                      disabled={isCreatingConnection}
                      className="w-full"
                    >
                      {isCreatingConnection ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Creating Connection...
                        </>
                      ) : (
                        "Create Connection"
                      )}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Status and Instructions */}
          <div className="space-y-6">
            {/* Connection Test Result */}
            {testResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    {testResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span>Connection Test</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={testResult.success ? "success" : "destructive"}
                      >
                        {testResult.success ? "Success" : "Failed"}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        {new Date(testResult.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <p className="text-sm">{testResult.message}</p>
                    {testResult.details && (
                      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                        <pre className="text-xs overflow-x-auto">
                          {JSON.stringify(testResult.details, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Webhook URL */}
            {webhookUrl && (
              <Card>
                <CardHeader>
                  <CardTitle>Webhook URL</CardTitle>
                  <CardDescription>
                    Use this URL to configure your {service.name} webhook
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2">
                    <Input
                      value={webhookUrl}
                      readOnly
                      className="font-mono text-sm"
                    />
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={copyWebhookUrl}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Setup Instructions */}
            <Card>
              <CardHeader>
                <CardTitle>Setup Instructions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {service.type === "telegram" && (
                  <div className="space-y-2">
                    <h4 className="font-medium">
                      Getting a Telegram Bot Token:
                    </h4>
                    <ol className="text-sm space-y-1 list-decimal list-inside text-gray-600 dark:text-gray-400">
                      <li>Open Telegram and search for @BotFather</li>
                      <li>Send /newbot command</li>
                      <li>Follow the instructions to create your bot</li>
                      <li>Copy the bot token provided</li>
                      <li>Paste it in the Bot Token field above</li>
                    </ol>
                    <a
                      href="https://core.telegram.org/bots#creating-a-new-bot"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                    >
                      View detailed guide
                      <ExternalLink className="ml-1 h-3 w-3" />
                    </a>
                  </div>
                )}

                {service.type === "whatsapp" && (
                  <div className="space-y-2">
                    <h4 className="font-medium">
                      Getting WhatsApp Business API Access:
                    </h4>
                    <ol className="text-sm space-y-1 list-decimal list-inside text-gray-600 dark:text-gray-400">
                      <li>Go to Facebook Developer Console</li>
                      <li>Create a new app or use existing one</li>
                      <li>Add WhatsApp Business API product</li>
                      <li>Get your access token and phone number ID</li>
                      <li>Configure webhook settings</li>
                    </ol>
                    <a
                      href="https://developers.facebook.com/docs/whatsapp/getting-started"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                    >
                      View detailed guide
                      <ExternalLink className="ml-1 h-3 w-3" />
                    </a>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
