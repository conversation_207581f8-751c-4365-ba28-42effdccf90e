import { NextRequest, NextResponse } from 'next/server';
import { getWebhookManager } from '@/lib/webhook-manager';
import { getN8nClient } from '@/lib/n8n-client';
import { ApiResponse, WhatsAppConfig, ConnectionTestResult } from '@/types/n8n';

const webhookManager = getWebhookManager();
const n8nClient = getN8nClient();

export async function POST(request: NextRequest) {
  try {
    const { action, ...data } = await request.json();

    switch (action) {
      case 'test_connection':
        return await testWhatsAppConnection(data);
      case 'create_webhook':
        return await createWhatsAppWebhook(data);
      case 'subscribe_webhook':
        return await subscribeWhatsAppWebhook(data);
      case 'get_phone_numbers':
        return await getPhoneNumbers(data);
      default:
        const response: ApiResponse = {
          success: false,
          error: 'Invalid action',
        };
        return NextResponse.json(response, { status: 400 });
    }
  } catch (error: any) {
    console.error('WhatsApp API error:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to process WhatsApp request',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

async function testWhatsAppConnection(config: WhatsAppConfig): Promise<NextResponse> {
  try {
    const { accessToken, phoneNumberId } = config;
    
    if (!accessToken || !phoneNumberId) {
      const response: ApiResponse = {
        success: false,
        error: 'Access token and phone number ID are required',
      };
      return NextResponse.json(response, { status: 400 });
    }

    // Test the connection by getting phone number info
    const whatsappResponse = await fetch(
      `https://graph.facebook.com/v18.0/${phoneNumberId}`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      }
    );

    const result = await whatsappResponse.json();

    if (whatsappResponse.status !== 200) {
      const testResult: ConnectionTestResult = {
        success: false,
        message: result.error?.message || 'Invalid credentials',
        timestamp: new Date().toISOString(),
      };
      
      const response: ApiResponse<ConnectionTestResult> = {
        success: true,
        data: testResult,
      };
      return NextResponse.json(response);
    }

    const testResult: ConnectionTestResult = {
      success: true,
      message: `Connected successfully to WhatsApp Business: ${result.display_phone_number}`,
      details: {
        phoneNumberId: result.id,
        displayPhoneNumber: result.display_phone_number,
        verifiedName: result.verified_name,
        qualityRating: result.quality_rating,
      },
      timestamp: new Date().toISOString(),
    };

    const response: ApiResponse<ConnectionTestResult> = {
      success: true,
      data: testResult,
    };
    return NextResponse.json(response);
  } catch (error: any) {
    const testResult: ConnectionTestResult = {
      success: false,
      message: error.message || 'Connection test failed',
      timestamp: new Date().toISOString(),
    };

    const response: ApiResponse<ConnectionTestResult> = {
      success: true,
      data: testResult,
    };
    return NextResponse.json(response);
  }
}

async function createWhatsAppWebhook(data: { businessName: string; config: WhatsAppConfig }): Promise<NextResponse> {
  try {
    const { businessName, config } = data;
    
    // Create the webhook workflow in n8n
    const workflow = await webhookManager.createWhatsAppWebhook(businessName);
    
    // Create credential for WhatsApp
    const credential = await n8nClient.createCredential({
      name: `${businessName} WhatsApp`,
      type: 'whatsAppApi',
      data: {
        accessToken: config.accessToken,
        phoneNumberId: config.phoneNumberId,
      },
    });

    const response: ApiResponse = {
      success: true,
      data: {
        workflow,
        credential,
        webhookPath: workflow.nodes.find(n => n.type === 'n8n-nodes-base.webhook')?.parameters.path,
      },
      message: 'WhatsApp webhook created successfully',
    };
    return NextResponse.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to create WhatsApp webhook',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

async function subscribeWhatsAppWebhook(data: { 
  accessToken: string; 
  phoneNumberId: string; 
  webhookUrl: string;
  verifyToken: string;
}): Promise<NextResponse> {
  try {
    const { accessToken, phoneNumberId, webhookUrl, verifyToken } = data;
    
    // Subscribe to webhook events
    const whatsappResponse = await fetch(
      `https://graph.facebook.com/v18.0/${phoneNumberId}/subscribed_apps`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscribed_fields: ['messages', 'message_deliveries', 'message_reads', 'message_reactions'],
        }),
      }
    );

    const result = await whatsappResponse.json();

    if (whatsappResponse.status !== 200) {
      const response: ApiResponse = {
        success: false,
        error: result.error?.message || 'Failed to subscribe webhook',
      };
      return NextResponse.json(response, { status: 400 });
    }

    const response: ApiResponse = {
      success: true,
      data: result,
      message: 'Webhook subscribed successfully',
    };
    return NextResponse.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to subscribe WhatsApp webhook',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

async function getPhoneNumbers(data: { accessToken: string; businessAccountId: string }): Promise<NextResponse> {
  try {
    const { accessToken, businessAccountId } = data;
    
    const whatsappResponse = await fetch(
      `https://graph.facebook.com/v18.0/${businessAccountId}/phone_numbers`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      }
    );

    const result = await whatsappResponse.json();

    if (whatsappResponse.status !== 200) {
      const response: ApiResponse = {
        success: false,
        error: result.error?.message || 'Failed to get phone numbers',
      };
      return NextResponse.json(response, { status: 400 });
    }

    const response: ApiResponse = {
      success: true,
      data: result.data || [],
    };
    return NextResponse.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to get phone numbers',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

// Handle webhook verification for WhatsApp
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const mode = searchParams.get('hub.mode');
    const token = searchParams.get('hub.verify_token');
    const challenge = searchParams.get('hub.challenge');

    // Verify the webhook (this should match your verify token)
    const verifyToken = process.env.WHATSAPP_VERIFY_TOKEN || 'your-verify-token';

    if (mode === 'subscribe' && token === verifyToken) {
      return new NextResponse(challenge, { status: 200 });
    } else {
      return new NextResponse('Forbidden', { status: 403 });
    }
  } catch (error: any) {
    console.error('WhatsApp webhook verification error:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
