"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Check, Star, Zap, Users, Building, Crown } from "lucide-react";
import { SubscriptionPlan } from "@/types/database";

export default function PricingPage() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("monthly");

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    try {
      // This would normally fetch from /api/public/plans
      // For now, we'll use the data from our database migration
      const mockPlans: SubscriptionPlan[] = [
        {
          id: "1",
          name: "Free",
          slug: "free",
          description: "Perfect for getting started with automation",
          price_monthly: 0,
          price_yearly: 0,
          stripe_price_id_monthly: null,
          stripe_price_id_yearly: null,
          max_workflows: 3,
          max_connections: 5,
          max_executions_per_month: 1000,
          features: [
            "Basic workflow templates",
            "Community support",
            "5 service connections",
          ],
          is_popular: false,
          is_active: true,
          sort_order: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: "2",
          name: "Starter",
          slug: "starter",
          description: "Great for small businesses and growing teams",
          price_monthly: 19,
          price_yearly: 190,
          stripe_price_id_monthly: null,
          stripe_price_id_yearly: null,
          max_workflows: 10,
          max_connections: 15,
          max_executions_per_month: 10000,
          features: [
            "All workflow templates",
            "Priority support",
            "15 service connections",
            "Analytics dashboard",
          ],
          is_popular: true,
          is_active: true,
          sort_order: 2,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: "3",
          name: "Pro",
          slug: "pro",
          description: "Advanced features for power users",
          price_monthly: 49,
          price_yearly: 490,
          stripe_price_id_monthly: null,
          stripe_price_id_yearly: null,
          max_workflows: 50,
          max_connections: 50,
          max_executions_per_month: 100000,
          features: [
            "Unlimited workflow templates",
            "Premium support",
            "50 service connections",
            "Advanced analytics",
            "Custom integrations",
          ],
          is_popular: false,
          is_active: true,
          sort_order: 3,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: "4",
          name: "Enterprise",
          slug: "enterprise",
          description: "Custom solutions for large organizations",
          price_monthly: 199,
          price_yearly: 1990,
          stripe_price_id_monthly: null,
          stripe_price_id_yearly: null,
          max_workflows: null,
          max_connections: null,
          max_executions_per_month: null,
          features: [
            "Unlimited everything",
            "Dedicated support",
            "Custom integrations",
            "SLA guarantee",
            "On-premise deployment",
          ],
          is_popular: false,
          is_active: true,
          sort_order: 4,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      setPlans(mockPlans);
    } catch (error) {
      console.error("Failed to load plans:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getPlanIcon = (planSlug: string) => {
    switch (planSlug) {
      case "free":
        return Zap;
      case "starter":
        return Users;
      case "pro":
        return Building;
      case "enterprise":
        return Crown;
      default:
        return Zap;
    }
  };

  const getPrice = (plan: SubscriptionPlan) => {
    if (billingCycle === "yearly") {
      return plan.price_yearly || plan.price_monthly * 12;
    }
    return plan.price_monthly;
  };

  const getYearlySavings = (plan: SubscriptionPlan) => {
    if (!plan.price_yearly || plan.price_monthly === 0) return 0;
    const monthlyTotal = plan.price_monthly * 12;
    const yearlySavings = monthlyTotal - plan.price_yearly;
    return Math.round((yearlySavings / monthlyTotal) * 100);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Simple, transparent pricing
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
          Choose the plan that's right for your automation needs. 
          Start free and upgrade as you grow.
        </p>

        {/* Billing Toggle */}
        <div className="flex items-center justify-center space-x-4">
          <span className={billingCycle === "monthly" ? "text-gray-900" : "text-gray-500"}>
            Monthly
          </span>
          <button
            onClick={() => setBillingCycle(billingCycle === "monthly" ? "yearly" : "monthly")}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              billingCycle === "yearly" ? "bg-blue-600" : "bg-gray-200"
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                billingCycle === "yearly" ? "translate-x-6" : "translate-x-1"
              }`}
            />
          </button>
          <span className={billingCycle === "yearly" ? "text-gray-900" : "text-gray-500"}>
            Yearly
          </span>
          {billingCycle === "yearly" && (
            <Badge className="bg-green-100 text-green-800">Save up to 20%</Badge>
          )}
        </div>
      </div>

      {/* Pricing Cards */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-2/3"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {[1, 2, 3, 4].map((j) => (
                    <div key={j} className="h-4 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {plans.map((plan) => {
            const IconComponent = getPlanIcon(plan.slug);
            const price = getPrice(plan);
            const yearlySavings = getYearlySavings(plan);
            
            return (
              <Card
                key={plan.id}
                className={`relative ${
                  plan.is_popular
                    ? "border-blue-500 shadow-lg scale-105"
                    : "border-gray-200"
                }`}
              >
                {plan.is_popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-600 text-white px-3 py-1">
                      <Star className="h-3 w-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="text-center pb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="h-6 w-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <CardDescription className="text-sm">
                    {plan.description}
                  </CardDescription>
                  
                  <div className="mt-4">
                    <div className="flex items-baseline justify-center">
                      <span className="text-3xl font-bold text-gray-900">
                        ${price}
                      </span>
                      {plan.price_monthly > 0 && (
                        <span className="text-gray-500 ml-1">
                          /{billingCycle === "yearly" ? "year" : "month"}
                        </span>
                      )}
                    </div>
                    {billingCycle === "yearly" && yearlySavings > 0 && (
                      <p className="text-sm text-green-600 mt-1">
                        Save {yearlySavings}% annually
                      </p>
                    )}
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="space-y-3 mb-6">
                    {/* Limits */}
                    <div className="text-sm text-gray-600 space-y-1">
                      <div className="flex justify-between">
                        <span>Workflows:</span>
                        <span className="font-medium">
                          {plan.max_workflows || "Unlimited"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Connections:</span>
                        <span className="font-medium">
                          {plan.max_connections || "Unlimited"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Executions/month:</span>
                        <span className="font-medium">
                          {plan.max_executions_per_month?.toLocaleString() || "Unlimited"}
                        </span>
                      </div>
                    </div>
                    
                    <hr className="border-gray-200" />
                    
                    {/* Features */}
                    <div className="space-y-2">
                      {(plan.features as string[]).map((feature, index) => (
                        <div key={index} className="flex items-start space-x-2">
                          <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-600">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <Link href="/auth/signup" className="block">
                    <Button
                      className={`w-full ${
                        plan.is_popular
                          ? "bg-blue-600 hover:bg-blue-700"
                          : "bg-gray-900 hover:bg-gray-800"
                      }`}
                    >
                      {plan.price_monthly === 0 ? "Get Started Free" : "Start Free Trial"}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* FAQ Section */}
      <div className="mt-20">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-gray-600">
            Everything you need to know about our pricing and plans.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              Can I change plans anytime?
            </h3>
            <p className="text-gray-600 text-sm">
              Yes, you can upgrade or downgrade your plan at any time. 
              Changes take effect immediately and we'll prorate the billing.
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              What happens if I exceed my limits?
            </h3>
            <p className="text-gray-600 text-sm">
              We'll notify you when you're approaching your limits. 
              You can upgrade your plan or additional usage will be charged at standard rates.
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              Is there a free trial?
            </h3>
            <p className="text-gray-600 text-sm">
              Yes! All paid plans come with a 14-day free trial. 
              No credit card required to get started.
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              Do you offer enterprise discounts?
            </h3>
            <p className="text-gray-600 text-sm">
              Yes, we offer custom pricing for large teams and enterprises. 
              Contact our sales team for a personalized quote.
            </p>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="mt-20 text-center">
        <div className="bg-gray-50 rounded-2xl px-8 py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to automate your workflows?
          </h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of businesses already using our platform to streamline 
            their operations and boost productivity.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/signup">
              <Button size="lg" className="px-8">
                Start Free Trial
              </Button>
            </Link>
            <Link href="/workflows">
              <Button size="lg" variant="outline" className="px-8">
                Browse Templates
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
