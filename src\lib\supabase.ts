import { createClient } from "@supabase/supabase-js";
import { createBrowserClient } from "@supabase/ssr";
import { Database } from "@/types/database";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Create a mock client for development when Supabase is not configured
const createMockClient = () => ({
  auth: {
    getUser: () => Promise.resolve({ data: { user: null }, error: null }),
    getSession: () => Promise.resolve({ data: { session: null }, error: null }),
    signUp: () =>
      Promise.resolve({
        data: { user: null, session: null },
        error: { message: "Supabase not configured" },
      }),
    signInWithPassword: () =>
      Promise.resolve({
        data: { user: null, session: null },
        error: { message: "Supabase not configured" },
      }),
    signOut: () => Promise.resolve({ error: null }),
    onAuthStateChange: (callback: any) => {
      // Call the callback immediately with no session to prevent loading state
      setTimeout(() => callback("SIGNED_OUT", null), 0);
      return {
        data: { subscription: { unsubscribe: () => {} } },
      };
    },
  },
  from: () => ({
    select: () => ({
      eq: () => ({
        single: () =>
          Promise.resolve({
            data: null,
            error: { message: "Supabase not configured" },
          }),
        order: () => ({
          limit: () => Promise.resolve({ data: [], error: null }),
        }),
      }),
    }),
    insert: () => ({
      select: () =>
        Promise.resolve({
          data: null,
          error: { message: "Supabase not configured" },
        }),
    }),
    update: () => ({
      eq: () => ({
        select: () => ({
          single: () =>
            Promise.resolve({
              data: null,
              error: { message: "Supabase not configured" },
            }),
        }),
      }),
    }),
    delete: () => ({
      eq: () =>
        Promise.resolve({
          data: null,
          error: { message: "Supabase not configured" },
        }),
    }),
  }),
});

// Initialize Supabase client or mock client
const initializeSupabase = () => {
  if (!supabaseUrl || !supabaseAnonKey) {
    console.warn(
      "Supabase environment variables not configured. Using mock client."
    );
    return createMockClient() as any;
  }

  return createClient<Database>(supabaseUrl, supabaseAnonKey);
};

// Browser client for client-side operations
export const createSupabaseBrowserClient = () => {
  if (!supabaseUrl || !supabaseAnonKey) {
    console.warn(
      "Supabase environment variables not configured. Using mock client."
    );
    return createMockClient() as any;
  }

  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey);
};

export const supabase = initializeSupabase();
export default supabase;
