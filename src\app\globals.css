@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --sidebar-bg: #1e293b;
  --sidebar-text: #f8fafc;
  --card-bg: #ffffff;
  --primary-blue: #3b82f6;
  --success-green: #10b981;
  --warning-orange: #f59e0b;
  --info-purple: #8b5cf6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", sans-serif;
}

/* Custom component styles inspired by the reference design */
.sidebar-nav {
  background: var(--sidebar-bg);
  color: var(--sidebar-text);
  min-height: 100vh;
  width: 16rem;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 40;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.main-content {
  margin-left: 16rem;
  min-height: 100vh;
  background: #f8fafc;
  flex: 1;
}

.stat-card {
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid transparent;
  transition: all 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.stat-card-blue {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #93c5fd;
}

.stat-card-green {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #6ee7b7;
}

.stat-card-orange {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #fcd34d;
}

.stat-card-purple {
  background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
  border-color: #c4b5fd;
}

.btn-primary {
  background: var(--primary-blue);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.content-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar-nav {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .main-content {
    margin-left: 0;
  }

  .content-card {
    padding: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }
}
