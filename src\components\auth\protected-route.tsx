"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/lib/auth-context";
import { AuthForm } from "./auth-form";
import { Loader2 } from "lucide-react";

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  const [authMode, setAuthMode] = useState<"signin" | "signup">("signin");

  // Development mode - skip auth for now
  const isDevelopment = process.env.NODE_ENV === "development";

  if (loading && !isDevelopment) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // In development, show the app without authentication
  if (isDevelopment) {
    return <>{children}</>;
  }

  if (!user) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">n8n</span>
              </div>
              <h1 className="text-3xl font-bold text-gray-900">Service Hub</h1>
            </div>
            <p className="text-gray-600">
              Connect external services to your n8n workflows
            </p>
          </div>
          <AuthForm
            mode={authMode}
            onToggleMode={() =>
              setAuthMode(authMode === "signin" ? "signup" : "signin")
            }
          />
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
