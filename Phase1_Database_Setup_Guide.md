# Phase 1: Database Setup Guide - Complete Implementation

## Overview
This guide provides step-by-step instructions for implementing the database changes required for Phase 1 of the SaaS transformation. This migration adds 6 new tables while preserving all existing data and functionality.

## 🚨 Important Prerequisites

### 1. Backup Your Database
Before running any migration, **always backup your database**:

```sql
-- In Supabase Dashboard, go to Settings > Database > Backups
-- Or use pg_dump if you have direct access
```

### 2. Verify Current Schema
Ensure your existing tables are working properly:
```sql
-- Check existing tables
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Verify existing RLS policies are working
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';
```

## 📋 Step-by-Step Implementation

### Step 1: Access Supabase SQL Editor
1. Go to your Supabase Dashboard
2. Navigate to **SQL Editor** in the left sidebar
3. Click **"New Query"**

### Step 2: Execute the Migration Script
1. Copy the entire contents of `supabase-saas-migration.sql`
2. Paste it into the SQL Editor
3. Click **"Run"** to execute the script

**Expected Output:**
```
Tables created successfully!
Categories inserted: 6
Plans inserted: 4
Database migration completed successfully!
```

### Step 3: Verify the Migration
Run these verification queries to ensure everything worked:

```sql
-- 1. Check all new tables exist
SELECT table_name, table_type
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN (
    'workflow_categories', 
    'workflow_templates', 
    'subscription_plans', 
    'user_subscriptions', 
    'user_workflows', 
    'usage_tracking'
)
ORDER BY table_name;

-- 2. Verify RLS is enabled on new tables
SELECT tablename, rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN (
    'workflow_categories', 
    'workflow_templates', 
    'subscription_plans', 
    'user_subscriptions', 
    'user_workflows', 
    'usage_tracking'
);

-- 3. Check initial data was inserted
SELECT name, slug, price_monthly FROM public.subscription_plans ORDER BY sort_order;
SELECT name, slug, description FROM public.workflow_categories ORDER BY sort_order;

-- 4. Verify existing tables are unchanged
SELECT COUNT(*) as existing_users FROM public.users;
SELECT COUNT(*) as existing_connections FROM public.service_connections;
```

## 📊 What This Migration Creates

### New Tables Added:

1. **`workflow_categories`** - Categories for organizing workflow templates
2. **`workflow_templates`** - The actual workflow templates for the marketplace
3. **`subscription_plans`** - Available subscription tiers (Free, Starter, Pro, Enterprise)
4. **`user_subscriptions`** - User's current subscription and billing info
5. **`user_workflows`** - User's installed workflows from templates
6. **`usage_tracking`** - Analytics and usage monitoring

### Enhanced Existing Table:

**`users`** table gets new columns:
- `onboarding_completed` - Track if user finished onboarding
- `selected_workflows` - Workflows chosen during signup
- `signup_source` - Where user came from
- `utm_source`, `utm_medium`, `utm_campaign` - Marketing attribution
- `is_admin` - Admin role flag

### Security Features:

✅ **Row Level Security (RLS)** enabled on all new tables
✅ **Public read access** for workflow templates and categories
✅ **User-scoped access** for subscriptions and workflows
✅ **Admin-only access** for management functions
✅ **Service role access** for backend operations

## 🔧 Post-Migration Tasks

### 1. Update TypeScript Types
After the migration, update your TypeScript definitions:

```typescript
// Add to src/types/database.ts
export type WorkflowCategory = Database["public"]["Tables"]["workflow_categories"]["Row"];
export type WorkflowTemplate = Database["public"]["Tables"]["workflow_templates"]["Row"];
export type SubscriptionPlan = Database["public"]["Tables"]["subscription_plans"]["Row"];
export type UserSubscription = Database["public"]["Tables"]["user_subscriptions"]["Row"];
export type UserWorkflow = Database["public"]["Tables"]["user_workflows"]["Row"];
export type UsageTracking = Database["public"]["Tables"]["usage_tracking"]["Row"];
```

### 2. Test Database Access
Create a simple test to verify the new tables work:

```typescript
// Test script - run in your development environment
import { supabase } from '@/lib/supabase'

async function testNewTables() {
  // Test public access to categories
  const { data: categories, error: catError } = await supabase
    .from('workflow_categories')
    .select('*')
  
  console.log('Categories:', categories?.length, 'Error:', catError)
  
  // Test public access to plans
  const { data: plans, error: planError } = await supabase
    .from('subscription_plans')
    .select('*')
  
  console.log('Plans:', plans?.length, 'Error:', planError)
}

testNewTables()
```

### 3. Set Up Your First Admin User
Make yourself an admin to access admin features:

```sql
-- Replace 'your-user-id' with your actual user ID from auth.users
UPDATE public.users 
SET is_admin = true 
WHERE id = 'your-user-id';
```

To find your user ID:
```sql
-- Find your user ID
SELECT id, email FROM auth.users WHERE email = '<EMAIL>';
```

## 🚨 Troubleshooting

### Common Issues and Solutions:

#### Issue: "relation already exists" errors
**Solution:** The script uses `IF NOT EXISTS` clauses, so this is safe to ignore.

#### Issue: RLS policy conflicts
**Solution:** Drop conflicting policies first:
```sql
-- Only if you have conflicts
DROP POLICY IF EXISTS "policy_name" ON table_name;
```

#### Issue: Permission denied errors
**Solution:** Ensure you're running as the database owner or have sufficient privileges.

#### Issue: Foreign key constraint errors
**Solution:** Verify the referenced tables exist:
```sql
SELECT table_name FROM information_schema.tables 
WHERE table_name IN ('users', 'workflow_templates');
```

### Rollback Plan (Emergency Only)
If something goes wrong, you can rollback:

```sql
-- EMERGENCY ROLLBACK - Only use if migration fails
DROP TABLE IF EXISTS public.usage_tracking CASCADE;
DROP TABLE IF EXISTS public.user_workflows CASCADE;
DROP TABLE IF EXISTS public.user_subscriptions CASCADE;
DROP TABLE IF EXISTS public.subscription_plans CASCADE;
DROP TABLE IF EXISTS public.workflow_templates CASCADE;
DROP TABLE IF EXISTS public.workflow_categories CASCADE;

-- Remove added columns from users table
ALTER TABLE public.users 
DROP COLUMN IF EXISTS onboarding_completed,
DROP COLUMN IF EXISTS selected_workflows,
DROP COLUMN IF EXISTS signup_source,
DROP COLUMN IF EXISTS utm_source,
DROP COLUMN IF EXISTS utm_medium,
DROP COLUMN IF EXISTS utm_campaign,
DROP COLUMN IF EXISTS is_admin;
```

## ✅ Success Criteria

Your migration is successful when:

1. ✅ All 6 new tables are created
2. ✅ RLS is enabled on all new tables
3. ✅ Initial data is inserted (6 categories, 4 plans)
4. ✅ Existing tables and data are unchanged
5. ✅ No errors in the verification queries
6. ✅ Your application still works normally

## 🎯 Next Steps

After completing this database migration:

1. **Update your local environment** with the new schema
2. **Test existing functionality** to ensure nothing broke
3. **Begin Phase 1 frontend work** (route restructuring)
4. **Start building the public API endpoints** that will use these tables

The database foundation is now ready for the SaaS transformation! 🚀

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify your Supabase project has sufficient permissions
3. Ensure you're using the latest version of the migration script
4. Check Supabase logs for detailed error messages

Remember: This migration is designed to be **non-destructive** - it only adds new functionality while preserving everything that currently works.
