export default function PublicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-white">
      {/* Public header will go here */}
      <header className="border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900">
                n8n Service Integration Hub
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <a
                href="/auth/signin"
                className="text-gray-500 hover:text-gray-700"
              >
                Sign in
              </a>
              <a
                href="/auth/signup"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Get Started
              </a>
            </div>
          </div>
        </div>
      </header>
      <main>{children}</main>
      {/* Public footer will go here */}
      <footer className="bg-gray-50 border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center text-gray-500">
            <p>&copy; 2024 n8n Service Integration Hub. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
