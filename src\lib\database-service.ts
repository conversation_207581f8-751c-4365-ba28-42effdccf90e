import { supabase } from "./supabase";
import { encryptCredentials, decryptCredentials } from "./encryption";
import {
  ServiceConnection,
  ServiceConnectionInsert,
  ServiceConnectionUpdate,
  ServiceTemplate,
  ConnectionLog,
  ConnectionLogInsert,
  User,
  UserUpdate,
} from "@/types/database";

export class DatabaseService {
  /**
   * Get all service connections for the current user
   */
  async getServiceConnections(userId: string): Promise<ServiceConnection[]> {
    try {
      const { data, error } = await supabase
        .from("service_connections")
        .select("*")
        .eq("user_id", userId)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching service connections:", error);
        // Return mock data if Supabase is not configured
        if (error.message === "Supabase not configured") {
          return this.getMockConnections(userId);
        }
        throw new Error("Failed to fetch service connections");
      }

      return data || [];
    } catch (error) {
      console.error("Database service error:", error);
      // Return mock data for development
      return this.getMockConnections(userId);
    }
  }

  /**
   * Get mock connections for development
   */
  private getMockConnections(userId: string): ServiceConnection[] {
    return [
      {
        id: "1",
        user_id: userId,
        service_type: "telegram",
        service_name: "Demo Telegram Bot",
        encrypted_credentials: "mock-encrypted-data",
        configuration: { demo: true },
        status: "connected",
        last_tested_at: new Date().toISOString(),
        error_message: null,
        webhook_url: "http://localhost:5678/webhook/demo-telegram",
        webhook_path: "demo-telegram",
        n8n_workflow_id: "demo-workflow-1",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: "2",
        user_id: userId,
        service_type: "whatsapp",
        service_name: "Demo WhatsApp Business",
        encrypted_credentials: "mock-encrypted-data",
        configuration: { demo: true },
        status: "error",
        last_tested_at: new Date().toISOString(),
        error_message: "Demo error - Supabase not configured",
        webhook_url: "http://localhost:5678/webhook/demo-whatsapp",
        webhook_path: "demo-whatsapp",
        n8n_workflow_id: "demo-workflow-2",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];
  }

  /**
   * Get a specific service connection by ID
   */
  async getServiceConnection(id: string): Promise<ServiceConnection | null> {
    try {
      const { data, error } = await supabase
        .from("service_connections")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return null; // Not found
        }
        console.error("Error fetching service connection:", error);
        throw new Error("Failed to fetch service connection");
      }

      return data;
    } catch (error) {
      console.error("Database service error:", error);
      throw error;
    }
  }

  /**
   * Create a new service connection
   */
  async createServiceConnection(
    connectionData: Omit<ServiceConnectionInsert, "encrypted_credentials"> & {
      credentials: Record<string, any>;
    }
  ): Promise<ServiceConnection> {
    try {
      const encryptedCredentials = encryptCredentials(
        connectionData.credentials
      );

      const insertData: ServiceConnectionInsert = {
        ...connectionData,
        encrypted_credentials: encryptedCredentials,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from("service_connections")
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error("Error creating service connection:", error);
        throw new Error("Failed to create service connection");
      }

      // Log the creation
      await this.logConnectionAction(
        data.id,
        data.user_id,
        "create",
        "success",
        "Connection created successfully"
      );

      return data;
    } catch (error) {
      console.error("Database service error:", error);
      throw error;
    }
  }

  /**
   * Update a service connection
   */
  async updateServiceConnection(
    id: string,
    updateData: Partial<
      Omit<ServiceConnectionUpdate, "encrypted_credentials">
    > & {
      credentials?: Record<string, any>;
    }
  ): Promise<ServiceConnection> {
    try {
      const updatePayload: ServiceConnectionUpdate = {
        ...updateData,
        updated_at: new Date().toISOString(),
      };

      if (updateData.credentials) {
        updatePayload.encrypted_credentials = encryptCredentials(
          updateData.credentials
        );
        delete (updateData as any).credentials;
      }

      const { data, error } = await supabase
        .from("service_connections")
        .update(updatePayload)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        console.error("Error updating service connection:", error);
        throw new Error("Failed to update service connection");
      }

      // Log the update
      await this.logConnectionAction(
        id,
        data.user_id,
        "update",
        "success",
        "Connection updated successfully"
      );

      return data;
    } catch (error) {
      console.error("Database service error:", error);
      throw error;
    }
  }

  /**
   * Delete a service connection
   */
  async deleteServiceConnection(id: string, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("service_connections")
        .delete()
        .eq("id", id)
        .eq("user_id", userId);

      if (error) {
        console.error("Error deleting service connection:", error);
        throw new Error("Failed to delete service connection");
      }

      // Log the deletion
      await this.logConnectionAction(
        id,
        userId,
        "delete",
        "success",
        "Connection deleted successfully"
      );
    } catch (error) {
      console.error("Database service error:", error);
      throw error;
    }
  }

  /**
   * Get decrypted credentials for a service connection
   */
  async getDecryptedCredentials(
    id: string,
    userId: string
  ): Promise<Record<string, any>> {
    try {
      const { data: connection, error } = await supabase
        .from("service_connections")
        .select("*")
        .eq("id", id)
        .eq("user_id", userId)
        .single();

      if (error || !connection) {
        throw new Error("Service connection not found");
      }

      return decryptCredentials(connection.encrypted_credentials);
    } catch (error) {
      console.error("Error decrypting credentials:", error);
      throw error;
    }
  }

  /**
   * Update connection status and error message
   */
  async updateConnectionStatus(
    id: string,
    userId: string,
    status: "connected" | "disconnected" | "error",
    errorMessage?: string
  ): Promise<void> {
    try {
      const updateData: ServiceConnectionUpdate = {
        status,
        error_message: errorMessage || null,
        last_tested_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from("service_connections")
        .update(updateData)
        .eq("id", id)
        .eq("user_id", userId);

      if (error) {
        console.error("Error updating connection status:", error);
        throw new Error("Failed to update connection status");
      }

      // Log the status change
      await this.logConnectionAction(
        id,
        userId,
        "status_update",
        status === "error" ? "error" : "success",
        errorMessage || `Status updated to ${status}`
      );
    } catch (error) {
      console.error("Database service error:", error);
      throw error;
    }
  }

  /**
   * Get all service templates
   */
  async getServiceTemplates(): Promise<ServiceTemplate[]> {
    try {
      const { data, error } = await supabase
        .from("service_templates")
        .select("*")
        .eq("is_active", true)
        .order("service_name");

      if (error) {
        console.error("Error fetching service templates:", error);
        // Return mock data if Supabase is not configured
        if (error.message === "Supabase not configured") {
          return this.getMockServiceTemplates();
        }
        throw new Error("Failed to fetch service templates");
      }

      return data || [];
    } catch (error) {
      console.error("Database service error:", error);
      // Return mock data for development
      return this.getMockServiceTemplates();
    }
  }

  /**
   * Get mock service templates for development
   */
  private getMockServiceTemplates(): ServiceTemplate[] {
    return [
      {
        id: "1",
        service_type: "telegram",
        service_name: "Telegram",
        description: "Connect your Telegram bot to receive and send messages",
        icon: "MessageSquare",
        auth_type: "api_key",
        config_fields: [
          {
            name: "botToken",
            label: "Bot Token",
            type: "password",
            required: true,
            placeholder: "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz",
            description: "Get this from @BotFather on Telegram",
          },
          {
            name: "botName",
            label: "Bot Name",
            type: "text",
            required: true,
            placeholder: "My Awesome Bot",
            description: "A friendly name for your bot",
          },
        ],
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: "2",
        service_type: "whatsapp",
        service_name: "WhatsApp Business",
        description: "Connect WhatsApp Business API for messaging automation",
        icon: "Phone",
        auth_type: "bearer_token",
        config_fields: [
          {
            name: "accessToken",
            label: "Access Token",
            type: "password",
            required: true,
            placeholder: "EAAxxxxxxxxxxxxxxx",
            description: "Get this from Facebook Developer Console",
          },
          {
            name: "phoneNumberId",
            label: "Phone Number ID",
            type: "text",
            required: true,
            placeholder: "1234567890123456",
            description: "Your WhatsApp Business phone number ID",
          },
          {
            name: "businessName",
            label: "Business Name",
            type: "text",
            required: true,
            placeholder: "My Business",
            description: "Name of your business",
          },
        ],
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];
  }

  /**
   * Log connection actions for audit trail
   */
  async logConnectionAction(
    connectionId: string,
    userId: string,
    action: string,
    status: "success" | "error",
    message?: string,
    details?: any
  ): Promise<void> {
    try {
      const logData: ConnectionLogInsert = {
        connection_id: connectionId,
        user_id: userId,
        action,
        status,
        message,
        details: details ? JSON.stringify(details) : null,
        created_at: new Date().toISOString(),
      };

      const { error } = await supabase.from("connection_logs").insert(logData);

      if (error) {
        console.error("Error logging connection action:", error);
        // Don't throw here as logging failures shouldn't break the main flow
      }
    } catch (error) {
      console.error("Logging error:", error);
      // Don't throw here as logging failures shouldn't break the main flow
    }
  }

  /**
   * Get connection logs for a specific connection
   */
  async getConnectionLogs(
    connectionId: string,
    userId: string
  ): Promise<ConnectionLog[]> {
    try {
      const { data, error } = await supabase
        .from("connection_logs")
        .select("*")
        .eq("connection_id", connectionId)
        .eq("user_id", userId)
        .order("created_at", { ascending: false })
        .limit(50);

      if (error) {
        console.error("Error fetching connection logs:", error);
        throw new Error("Failed to fetch connection logs");
      }

      return data || [];
    } catch (error) {
      console.error("Database service error:", error);
      throw error;
    }
  }

  /**
   * Get user profile by ID
   */
  async getUserProfile(userId: string): Promise<User | null> {
    try {
      const { data, error } = await supabase
        .from("users")
        .select("*")
        .eq("id", userId)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return null; // Not found
        }
        console.error("Error fetching user profile:", error);
        throw new Error("Failed to fetch user profile");
      }

      return data;
    } catch (error) {
      console.error("Database service error:", error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(
    userId: string,
    updates: Partial<UserUpdate>
  ): Promise<User> {
    try {
      const { data, error } = await supabase
        .from("users")
        .update(updates)
        .eq("id", userId)
        .select()
        .single();

      if (error) {
        console.error("Error updating user profile:", error);
        throw new Error("Failed to update user profile");
      }

      return data;
    } catch (error) {
      console.error("Database service error:", error);
      throw error;
    }
  }
}

// Singleton instance
let databaseService: DatabaseService | null = null;

export function getDatabaseService(): DatabaseService {
  if (!databaseService) {
    databaseService = new DatabaseService();
  }
  return databaseService;
}

export default DatabaseService;
