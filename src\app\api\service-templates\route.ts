import { NextRequest, NextResponse } from 'next/server';
import { getDatabaseService } from '@/lib/database-service';
import { ApiResponse } from '@/types/n8n';
import { ServiceTemplate } from '@/types/database';

const databaseService = getDatabaseService();

export async function GET(request: NextRequest) {
  try {
    const templates = await databaseService.getServiceTemplates();
    
    const response: ApiResponse<ServiceTemplate[]> = {
      success: true,
      data: templates,
    };
    return NextResponse.json(response);
  } catch (error: any) {
    console.error('Error fetching service templates:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to fetch service templates',
    };
    return NextResponse.json(response, { status: 500 });
  }
}
