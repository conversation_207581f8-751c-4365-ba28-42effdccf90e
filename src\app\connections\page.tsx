"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { useAuth } from "@/lib/auth-context";
import {
  MessageSquare,
  Phone,
  Mail,
  Slack,
  Plus,
  CheckCircle,
  AlertCircle,
  Clock,
  Search,
} from "lucide-react";
import { ExternalService, ServiceConnection } from "@/types/n8n";
import {
  ServiceTemplate,
  ServiceConnection as DBServiceConnection,
} from "@/types/database";

// This will be loaded from Supabase
const availableServices: ExternalService[] = [
  {
    id: "telegram",
    name: "Telegram",
    type: "telegram",
    icon: "MessageSquare",
    description: "Connect your Telegram bot to receive and send messages",
    authType: "api_key",
    configFields: [
      {
        name: "botToken",
        label: "Bot Token",
        type: "password",
        required: true,
        placeholder: "Enter your Telegram bot token",
        description: "Get this from @BotFather on Telegram",
      },
      {
        name: "botName",
        label: "Bot Name",
        type: "text",
        required: true,
        placeholder: "My Awesome Bot",
        description: "A friendly name for your bot",
      },
    ],
    isConnected: false,
  },
  {
    id: "whatsapp",
    name: "WhatsApp Business",
    type: "whatsapp",
    icon: "Phone",
    description: "Connect WhatsApp Business API for messaging automation",
    authType: "bearer_token",
    configFields: [
      {
        name: "accessToken",
        label: "Access Token",
        type: "password",
        required: true,
        placeholder: "Enter your WhatsApp access token",
        description: "Get this from Facebook Developer Console",
      },
      {
        name: "phoneNumberId",
        label: "Phone Number ID",
        type: "text",
        required: true,
        placeholder: "Enter phone number ID",
        description: "Your WhatsApp Business phone number ID",
      },
      {
        name: "businessName",
        label: "Business Name",
        type: "text",
        required: true,
        placeholder: "My Business",
        description: "Name of your business",
      },
    ],
    isConnected: false,
  },
  {
    id: "email",
    name: "Email (SMTP)",
    type: "email",
    icon: "Mail",
    description: "Send automated emails via SMTP",
    authType: "basic_auth",
    configFields: [
      {
        name: "host",
        label: "SMTP Host",
        type: "text",
        required: true,
        placeholder: "smtp.gmail.com",
        description: "Your SMTP server hostname",
      },
      {
        name: "port",
        label: "Port",
        type: "text",
        required: true,
        placeholder: "587",
        description: "SMTP port (usually 587 or 465)",
      },
      {
        name: "username",
        label: "Username",
        type: "text",
        required: true,
        placeholder: "<EMAIL>",
        description: "Your email address",
      },
      {
        name: "password",
        label: "Password",
        type: "password",
        required: true,
        placeholder: "Enter your password or app password",
        description: "Your email password or app-specific password",
      },
    ],
    isConnected: false,
  },
  {
    id: "slack",
    name: "Slack",
    type: "slack",
    icon: "Slack",
    description: "Send messages and notifications to Slack channels",
    authType: "oauth",
    configFields: [
      {
        name: "webhookUrl",
        label: "Webhook URL",
        type: "url",
        required: true,
        placeholder: "https://hooks.slack.com/services/...",
        description: "Your Slack incoming webhook URL",
      },
      {
        name: "channel",
        label: "Default Channel",
        type: "text",
        required: false,
        placeholder: "#general",
        description: "Default channel for messages",
      },
    ],
    isConnected: false,
  },
];

function ConnectionsContent() {
  const { user } = useAuth();
  const searchParams = useSearchParams();
  const selectedService = searchParams?.get("service");

  const [connections, setConnections] = useState<DBServiceConnection[]>([]);
  const [serviceTemplates, setServiceTemplates] = useState<ServiceTemplate[]>(
    []
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    if (!user) return;

    try {
      // Load user-specific connections from Supabase
      const connectionsResponse = await fetch(
        `/api/connections?userId=${user.id}`
      );
      const connectionsResult = await connectionsResponse.json();

      if (connectionsResult.success) {
        setConnections(connectionsResult.data || []);
      }

      // Load service templates from Supabase
      const templatesResponse = await fetch("/api/service-templates");
      const templatesResult = await templatesResponse.json();

      if (templatesResult.success) {
        setServiceTemplates(templatesResult.data || []);
      }
    } catch (error) {
      console.error("Failed to load data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Convert service templates to the format expected by the UI
  const availableServicesFromDB = serviceTemplates.map((template) => ({
    id: template.id,
    name: template.service_name,
    type: template.service_type as any,
    icon: template.icon,
    description: template.description,
    authType: template.auth_type as any,
    configFields: template.config_fields as any,
    isConnected: connections.some(
      (conn) => conn.service_type === template.service_type
    ),
  }));

  // Use database templates if available, otherwise fall back to hardcoded ones
  const servicesToShow =
    serviceTemplates.length > 0 ? availableServicesFromDB : availableServices;

  const filteredServices = servicesToShow.filter(
    (service) =>
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getServiceIcon = (iconName: string) => {
    const icons = {
      MessageSquare,
      Phone,
      Mail,
      Slack,
    };
    return icons[iconName as keyof typeof icons] || MessageSquare;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "connected":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "disconnected":
        return <Clock className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "connected":
        return <Badge variant="success">Connected</Badge>;
      case "error":
        return <Badge variant="destructive">Error</Badge>;
      case "disconnected":
        return <Badge variant="secondary">Disconnected</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Service Connections
              </h1>
              <p className="text-gray-600 mt-1">
                Connect and manage external services for your workflows
              </p>
            </div>
            <Link href="/connections/setup">
              <button className="btn-primary flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>New Connection</span>
              </button>
            </Link>
          </div>
        </div>
        {/* Search and Filter */}
        <div className="content-card mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Existing Connections */}
        {connections.length > 0 && (
          <div className="content-card mb-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-6 h-6 bg-green-100 rounded flex items-center justify-center">
                <CheckCircle className="w-4 h-4 text-green-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">
                Your Connections ({connections.length})
              </h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {connections.map((connection) => {
                const service = servicesToShow.find(
                  (s) => s.type === connection.service_type
                );
                const IconComponent = service
                  ? getServiceIcon(service.icon)
                  : MessageSquare;

                return (
                  <div
                    key={connection.id}
                    className="stat-card bg-white hover:shadow-md transition-all"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <IconComponent className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900">
                            {connection.service_name}
                          </h4>
                          <p className="text-sm text-gray-500">
                            {service?.name || connection.service_type}
                          </p>
                        </div>
                      </div>
                      {getStatusIcon(connection.status)}
                    </div>
                    <div className="flex items-center justify-between">
                      {getStatusBadge(connection.status)}
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        Configure
                      </Button>
                    </div>
                    {connection.error_message && (
                      <p className="text-sm text-red-600 mt-2">
                        {connection.error_message}
                      </p>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Available Services */}
        <div className="content-card">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
              <Plus className="w-4 h-4 text-blue-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900">
              Available Services
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredServices.map((service) => {
              const IconComponent = getServiceIcon(service.icon);
              const isHighlighted = selectedService === service.type;

              return (
                <div
                  key={service.id}
                  className={`stat-card bg-white hover:shadow-md transition-all ${
                    isHighlighted ? "ring-2 ring-blue-500" : ""
                  }`}
                >
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <IconComponent className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 text-lg">
                        {service.name}
                      </h4>
                      <p className="text-gray-600 text-sm">
                        {service.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {service.authType.replace("_", " ")}
                    </span>
                    <Link href={`/connections/setup?service=${service.type}`}>
                      <button className="btn-primary text-sm px-4 py-2 flex items-center space-x-2">
                        <Plus className="w-4 h-4" />
                        <span>Connect</span>
                      </button>
                    </Link>
                  </div>
                </div>
              );
            })}
          </div>

          {filteredServices.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">
                No services found matching your search.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function ConnectionsPage() {
  return (
    <ProtectedRoute>
      <ConnectionsContent />
    </ProtectedRoute>
  );
}
