import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a Supabase client for public access (no auth required)
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params

    // Get the workflow template by slug
    const { data, error } = await supabase
      .from('workflow_templates')
      .select(`
        id, name, slug, description, long_description, category, tags, icon,
        featured_image, demo_video_url, required_services, difficulty_level,
        estimated_setup_time, pricing_tier, is_featured, view_count,
        install_count, rating, created_at, updated_at
      `)
      .eq('slug', slug)
      .eq('is_active', true)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Workflow template not found' },
          { status: 404 }
        )
      }
      console.error('Database error:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Increment view count
    await supabase
      .from('workflow_templates')
      .update({ view_count: (data.view_count || 0) + 1 })
      .eq('id', data.id)

    return NextResponse.json({
      success: true,
      data
    })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
