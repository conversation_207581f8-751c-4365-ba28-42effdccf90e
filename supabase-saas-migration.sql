-- ============================================================================
-- SUPABASE SAAS MIGRATION - PHASE 1 DATABASE SETUP
-- ============================================================================
-- This script adds the new SaaS tables to the existing n8n Service Integration Hub
-- Run this ENTIRE script in your Supabase SQL Editor
-- 
-- IMPORTANT: This preserves all existing data and tables
-- ============================================================================

-- ============================================================================
-- STEP 1: CREATE NEW SAAS TABLES
-- ============================================================================

-- Create workflow categories table
CREATE TABLE IF NOT EXISTS public.workflow_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    slug VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(20),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workflow templates table
CREATE TABLE IF NOT EXISTS public.workflow_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT NOT NULL,
    long_description TEXT,
    category VARCHAR(50) NOT NULL,
    tags TEXT[] DEFAULT '{}',
    icon VARCHAR(50) NOT NULL,
    featured_image TEXT,
    demo_video_url TEXT,
    n8n_workflow_json JSONB NOT NULL,
    required_services TEXT[] NOT NULL,
    difficulty_level VARCHAR(20) DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    estimated_setup_time INTEGER DEFAULT 10,
    pricing_tier VARCHAR(20) DEFAULT 'free' CHECK (pricing_tier IN ('free', 'starter', 'pro', 'enterprise')),
    is_featured BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    view_count INTEGER DEFAULT 0,
    install_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.0 CHECK (rating >= 0 AND rating <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscription plans table
CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    slug VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    price_monthly DECIMAL(10,2) NOT NULL,
    price_yearly DECIMAL(10,2),
    stripe_price_id_monthly VARCHAR(100),
    stripe_price_id_yearly VARCHAR(100),
    max_workflows INTEGER, -- NULL means unlimited
    max_connections INTEGER, -- NULL means unlimited
    max_executions_per_month INTEGER, -- NULL means unlimited
    features JSONB DEFAULT '[]',
    is_popular BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user subscriptions table
CREATE TABLE IF NOT EXISTS public.user_subscriptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    plan_id UUID NOT NULL REFERENCES public.subscription_plans(id),
    stripe_subscription_id VARCHAR(100) UNIQUE,
    stripe_customer_id VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'past_due', 'unpaid', 'trialing')),
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    cancel_at_period_end BOOLEAN DEFAULT false,
    billing_cycle VARCHAR(10) DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'yearly')),
    workflow_count INTEGER DEFAULT 0,
    connection_count INTEGER DEFAULT 0,
    executions_this_month INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user workflows table
CREATE TABLE IF NOT EXISTS public.user_workflows (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    template_id UUID NOT NULL REFERENCES public.workflow_templates(id),
    n8n_workflow_id VARCHAR(100),
    custom_name VARCHAR(100),
    is_active BOOLEAN DEFAULT false,
    configuration JSONB DEFAULT '{}',
    last_execution_at TIMESTAMP WITH TIME ZONE,
    execution_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create usage tracking table for analytics
CREATE TABLE IF NOT EXISTS public.usage_tracking (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    workflow_id UUID REFERENCES public.user_workflows(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 2: UPDATE EXISTING USERS TABLE
-- ============================================================================

-- Add new columns to existing users table
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS selected_workflows UUID[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS signup_source VARCHAR(50),
ADD COLUMN IF NOT EXISTS utm_source VARCHAR(100),
ADD COLUMN IF NOT EXISTS utm_medium VARCHAR(100),
ADD COLUMN IF NOT EXISTS utm_campaign VARCHAR(100),
ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT false;

-- ============================================================================
-- STEP 3: CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Workflow templates indexes
CREATE INDEX IF NOT EXISTS idx_workflow_templates_category ON public.workflow_templates(category);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_slug ON public.workflow_templates(slug);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_active ON public.workflow_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_featured ON public.workflow_templates(is_featured);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_pricing_tier ON public.workflow_templates(pricing_tier);

-- User subscriptions indexes
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_plan_id ON public.user_subscriptions(plan_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON public.user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_stripe_id ON public.user_subscriptions(stripe_subscription_id);

-- User workflows indexes
CREATE INDEX IF NOT EXISTS idx_user_workflows_user_id ON public.user_workflows(user_id);
CREATE INDEX IF NOT EXISTS idx_user_workflows_template_id ON public.user_workflows(template_id);
CREATE INDEX IF NOT EXISTS idx_user_workflows_active ON public.user_workflows(is_active);

-- Usage tracking indexes
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_id ON public.usage_tracking(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_workflow_id ON public.usage_tracking(workflow_id);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_timestamp ON public.usage_tracking(timestamp);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_event_type ON public.usage_tracking(event_type);

-- Workflow categories indexes
CREATE INDEX IF NOT EXISTS idx_workflow_categories_slug ON public.workflow_categories(slug);
CREATE INDEX IF NOT EXISTS idx_workflow_categories_active ON public.workflow_categories(is_active);

-- ============================================================================
-- STEP 4: CREATE UPDATED_AT TRIGGERS
-- ============================================================================

-- Drop existing triggers if they exist, then create new ones
DROP TRIGGER IF EXISTS update_workflow_categories_updated_at ON public.workflow_categories;
CREATE TRIGGER update_workflow_categories_updated_at
    BEFORE UPDATE ON public.workflow_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_workflow_templates_updated_at ON public.workflow_templates;
CREATE TRIGGER update_workflow_templates_updated_at
    BEFORE UPDATE ON public.workflow_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subscription_plans_updated_at ON public.subscription_plans;
CREATE TRIGGER update_subscription_plans_updated_at
    BEFORE UPDATE ON public.subscription_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_subscriptions_updated_at ON public.user_subscriptions;
CREATE TRIGGER update_user_subscriptions_updated_at
    BEFORE UPDATE ON public.user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_workflows_updated_at ON public.user_workflows;
CREATE TRIGGER update_user_workflows_updated_at
    BEFORE UPDATE ON public.user_workflows
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- STEP 5: ENABLE ROW LEVEL SECURITY
-- ============================================================================

-- Enable RLS on all new tables
ALTER TABLE public.workflow_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workflow_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_tracking ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 6: CREATE RLS POLICIES
-- ============================================================================

-- Workflow Categories Policies (Public read access)
CREATE POLICY "Anyone can view active workflow categories" ON public.workflow_categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage workflow categories" ON public.workflow_categories
    FOR ALL USING (
        auth.uid() IN (
            SELECT id FROM public.users WHERE is_admin = true
        )
    );

-- Workflow Templates Policies (Public read access for active templates)
CREATE POLICY "Anyone can view active workflow templates" ON public.workflow_templates
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage workflow templates" ON public.workflow_templates
    FOR ALL USING (
        auth.uid() IN (
            SELECT id FROM public.users WHERE is_admin = true
        )
    );

-- Subscription Plans Policies (Public read access for active plans)
CREATE POLICY "Anyone can view active subscription plans" ON public.subscription_plans
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage subscription plans" ON public.subscription_plans
    FOR ALL USING (
        auth.uid() IN (
            SELECT id FROM public.users WHERE is_admin = true
        )
    );

-- User Subscriptions Policies (User can only access their own)
CREATE POLICY "Users can view their own subscription" ON public.user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own subscription" ON public.user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all subscriptions" ON public.user_subscriptions
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view all subscriptions" ON public.user_subscriptions
    FOR SELECT USING (
        auth.uid() IN (
            SELECT id FROM public.users WHERE is_admin = true
        )
    );

-- User Workflows Policies (User can only access their own)
CREATE POLICY "Users can view their own workflows" ON public.user_workflows
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own workflows" ON public.user_workflows
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own workflows" ON public.user_workflows
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own workflows" ON public.user_workflows
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all user workflows" ON public.user_workflows
    FOR SELECT USING (
        auth.uid() IN (
            SELECT id FROM public.users WHERE is_admin = true
        )
    );

-- Usage Tracking Policies (User can only access their own)
CREATE POLICY "Users can view their own usage data" ON public.usage_tracking
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can insert usage data" ON public.usage_tracking
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

CREATE POLICY "Admins can view all usage data" ON public.usage_tracking
    FOR SELECT USING (
        auth.uid() IN (
            SELECT id FROM public.users WHERE is_admin = true
        )
    );

-- ============================================================================
-- STEP 7: INSERT INITIAL DATA
-- ============================================================================

-- Insert default workflow categories
INSERT INTO public.workflow_categories (name, slug, description, icon, color, sort_order) VALUES
('Customer Support', 'customer-support', 'Automate customer service and support workflows', 'Headphones', '#3B82F6', 1),
('E-commerce', 'ecommerce', 'Streamline online store operations and order management', 'ShoppingCart', '#10B981', 2),
('Social Media', 'social-media', 'Manage social media presence and engagement', 'Share2', '#8B5CF6', 3),
('Lead Generation', 'lead-generation', 'Capture and nurture leads automatically', 'Target', '#F59E0B', 4),
('Data Sync', 'data-sync', 'Keep data synchronized across platforms', 'RefreshCw', '#EF4444', 5),
('Notifications', 'notifications', 'Send alerts and notifications across channels', 'Bell', '#06B6D4', 6)
ON CONFLICT (slug) DO NOTHING;

-- Insert default subscription plans
INSERT INTO public.subscription_plans (name, slug, description, price_monthly, price_yearly, max_workflows, max_connections, max_executions_per_month, features, is_popular, sort_order) VALUES
(
    'Free', 
    'free', 
    'Perfect for getting started with automation', 
    0.00, 
    0.00, 
    3, 
    5, 
    1000, 
    '["Basic workflow templates", "Community support", "5 service connections"]',
    false,
    1
),
(
    'Starter', 
    'starter', 
    'Great for small businesses and growing teams', 
    19.00, 
    190.00, 
    10, 
    15, 
    10000, 
    '["All workflow templates", "Priority support", "15 service connections", "Analytics dashboard"]',
    true,
    2
),
(
    'Pro', 
    'pro', 
    'Advanced features for power users', 
    49.00, 
    490.00, 
    50, 
    50, 
    100000, 
    '["Unlimited workflow templates", "Premium support", "50 service connections", "Advanced analytics", "Custom integrations"]',
    false,
    3
),
(
    'Enterprise', 
    'enterprise', 
    'Custom solutions for large organizations', 
    199.00, 
    1990.00, 
    NULL, 
    NULL, 
    NULL, 
    '["Unlimited everything", "Dedicated support", "Custom integrations", "SLA guarantee", "On-premise deployment"]',
    false,
    4
)
ON CONFLICT (slug) DO NOTHING;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify all tables were created
SELECT 'Tables created successfully!' as status;

SELECT table_name, table_type
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN (
    'workflow_categories', 
    'workflow_templates', 
    'subscription_plans', 
    'user_subscriptions', 
    'user_workflows', 
    'usage_tracking'
)
ORDER BY table_name;

-- Verify RLS is enabled
SELECT
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN (
    'workflow_categories', 
    'workflow_templates', 
    'subscription_plans', 
    'user_subscriptions', 
    'user_workflows', 
    'usage_tracking'
)
ORDER BY tablename;

-- Verify initial data was inserted
SELECT 'Categories inserted:' as info, COUNT(*) as count FROM public.workflow_categories;
SELECT 'Plans inserted:' as info, COUNT(*) as count FROM public.subscription_plans;

-- Show the inserted categories and plans
SELECT name, slug, description FROM public.workflow_categories ORDER BY sort_order;
SELECT name, slug, price_monthly, max_workflows, max_connections FROM public.subscription_plans ORDER BY sort_order;

SELECT 'Database migration completed successfully!' as final_status;
