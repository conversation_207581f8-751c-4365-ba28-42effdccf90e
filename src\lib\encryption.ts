import CryptoJS from 'crypto-js';

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-encryption-key-change-in-production';

if (!process.env.ENCRYPTION_KEY && process.env.NODE_ENV === 'production') {
  throw new Error('ENCRYPTION_KEY environment variable is required in production');
}

/**
 * Encrypts sensitive data before storing in the database
 */
export function encryptData(data: string): string {
  try {
    const encrypted = CryptoJS.AES.encrypt(data, ENCRYPTION_KEY).toString();
    return encrypted;
  } catch (error) {
    console.error('Encryption failed:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypts sensitive data retrieved from the database
 */
export function decryptData(encryptedData: string): string {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
    const decrypted = bytes.toString(CryptoJS.enc.Utf8);
    
    if (!decrypted) {
      throw new Error('Failed to decrypt data - invalid key or corrupted data');
    }
    
    return decrypted;
  } catch (error) {
    console.error('Decryption failed:', error);
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Encrypts an object containing sensitive credentials
 */
export function encryptCredentials(credentials: Record<string, any>): string {
  try {
    const jsonString = JSON.stringify(credentials);
    return encryptData(jsonString);
  } catch (error) {
    console.error('Credential encryption failed:', error);
    throw new Error('Failed to encrypt credentials');
  }
}

/**
 * Decrypts credentials object from encrypted string
 */
export function decryptCredentials(encryptedCredentials: string): Record<string, any> {
  try {
    const decryptedString = decryptData(encryptedCredentials);
    return JSON.parse(decryptedString);
  } catch (error) {
    console.error('Credential decryption failed:', error);
    throw new Error('Failed to decrypt credentials');
  }
}

/**
 * Masks sensitive data for display purposes
 */
export function maskSensitiveData(data: string, visibleChars: number = 4): string {
  if (!data || data.length <= visibleChars) {
    return '*'.repeat(data?.length || 8);
  }
  
  const visible = data.slice(-visibleChars);
  const masked = '*'.repeat(Math.max(8, data.length - visibleChars));
  return masked + visible;
}

/**
 * Validates if a string is properly encrypted
 */
export function isEncrypted(data: string): boolean {
  try {
    // Try to decrypt - if it fails, it's not encrypted or corrupted
    decryptData(data);
    return true;
  } catch {
    return false;
  }
}
