import { NextRequest, NextResponse } from 'next/server';
import { getN8nClient } from '@/lib/n8n-client';
import { ApiResponse, N8nCredential } from '@/types/n8n';

const n8nClient = getN8nClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const credentialId = searchParams.get('id');

    if (credentialId) {
      // Get specific credential
      const credential = await n8nClient.getCredential(credentialId);
      const response: ApiResponse<N8nCredential> = {
        success: true,
        data: credential,
      };
      return NextResponse.json(response);
    } else {
      // Get all credentials
      const credentials = await n8nClient.getCredentials();
      const response: ApiResponse<N8nCredential[]> = {
        success: true,
        data: credentials,
      };
      return NextResponse.json(response);
    }
  } catch (error: any) {
    console.error('Error fetching credentials:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to fetch credentials',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const credentialData = await request.json();
    
    const credential = await n8nClient.createCredential(credentialData);
    const response: ApiResponse<N8nCredential> = {
      success: true,
      data: credential,
      message: 'Credential created successfully',
    };
    return NextResponse.json(response, { status: 201 });
  } catch (error: any) {
    console.error('Error creating credential:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to create credential',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const credentialId = searchParams.get('id');
    
    if (!credentialId) {
      const response: ApiResponse = {
        success: false,
        error: 'Credential ID is required',
      };
      return NextResponse.json(response, { status: 400 });
    }

    const credentialData = await request.json();
    const credential = await n8nClient.updateCredential(credentialId, credentialData);
    
    const response: ApiResponse<N8nCredential> = {
      success: true,
      data: credential,
      message: 'Credential updated successfully',
    };
    return NextResponse.json(response);
  } catch (error: any) {
    console.error('Error updating credential:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to update credential',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const credentialId = searchParams.get('id');
    
    if (!credentialId) {
      const response: ApiResponse = {
        success: false,
        error: 'Credential ID is required',
      };
      return NextResponse.json(response, { status: 400 });
    }

    await n8nClient.deleteCredential(credentialId);
    
    const response: ApiResponse = {
      success: true,
      message: 'Credential deleted successfully',
    };
    return NextResponse.json(response);
  } catch (error: any) {
    console.error('Error deleting credential:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to delete credential',
    };
    return NextResponse.json(response, { status: 500 });
  }
}
