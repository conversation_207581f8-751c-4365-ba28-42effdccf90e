# Supabase Setup Guide

This guide will help you set up Supabase as the secure database solution for storing service connections and credentials.

## 🚀 Quick Setup

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Sign up/Login to your account
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - **Name**: `n8n-service-connections`
   - **Database Password**: Generate a strong password
   - **Region**: Choose closest to your location
6. Click "Create new project"

### 2. Get Project Credentials

Once your project is created:

1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (e.g., `https://your-project.supabase.co`)
   - **Anon public key** (starts with `eyJ...`)
   - **Service role key** (starts with `eyJ...`) - **Keep this secret!**

### 3. Update Environment Variables

Update your `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Security (Generate a strong encryption key)
ENCRYPTION_KEY=your-32-character-encryption-key-here
```

### 4. Set Up Database Schema

1. Go to **SQL Editor** in your Supabase dashboard
2. Copy the contents of `supabase-schema.sql` from this project
3. Paste it into the SQL editor
4. Click "Run" to execute the schema

## 🔐 Security Configuration

### Row Level Security (RLS)

The schema automatically enables RLS with these policies:

- **service_connections**: Users can only access their own connections
- **connection_logs**: Users can only view logs for their connections
- **service_templates**: All authenticated users can read templates

### Encryption

- All sensitive credentials are encrypted before storage using AES encryption
- The encryption key is stored as an environment variable
- Only the application can decrypt the stored credentials

## 📊 Database Tables

### service_connections
Stores encrypted service credentials and connection metadata.

```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key to auth.users)
- service_type (VARCHAR) - 'telegram', 'whatsapp', etc.
- service_name (VARCHAR) - User-friendly name
- encrypted_credentials (TEXT) - AES encrypted JSON
- configuration (JSONB) - Non-sensitive config
- status (ENUM) - 'connected', 'disconnected', 'error'
- webhook_url (TEXT) - Generated webhook URL
- n8n_workflow_id (VARCHAR) - Associated n8n workflow
- timestamps (created_at, updated_at)
```

### service_templates
Predefined service configurations for the UI.

```sql
- id (UUID, Primary Key)
- service_type (VARCHAR) - Service identifier
- service_name (VARCHAR) - Display name
- description (TEXT) - Service description
- icon (VARCHAR) - Icon name for UI
- auth_type (VARCHAR) - Authentication method
- config_fields (JSONB) - Form field definitions
- is_active (BOOLEAN) - Template availability
```

### connection_logs
Audit trail for connection actions.

```sql
- id (UUID, Primary Key)
- connection_id (UUID, Foreign Key)
- action (VARCHAR) - 'create', 'update', 'test', etc.
- status (ENUM) - 'success', 'error'
- message (TEXT) - Action description
- details (JSONB) - Additional metadata
- created_at (TIMESTAMP)
```

## 🔧 API Usage

### Creating a Connection

```typescript
const connection = await databaseService.createServiceConnection({
  user_id: userId,
  service_type: 'telegram',
  service_name: 'My Bot',
  credentials: {
    botToken: 'secret-token',
    botName: 'My Bot'
  },
  status: 'connected'
});
```

### Retrieving Decrypted Credentials

```typescript
const credentials = await databaseService.getDecryptedCredentials(connectionId);
// Returns: { botToken: 'secret-token', botName: 'My Bot' }
```

### Updating Connection Status

```typescript
await databaseService.updateConnectionStatus(
  connectionId, 
  'error', 
  'Invalid token'
);
```

## 🛡️ Security Best Practices

### Environment Variables
- Never commit `.env.local` to version control
- Use strong, unique encryption keys (32+ characters)
- Rotate service role keys periodically

### Database Access
- Service role key should only be used server-side
- Anon key is safe for client-side use (with RLS)
- Monitor database access logs regularly

### Credential Storage
- All sensitive data is encrypted before storage
- Encryption keys are separate from database
- Use secure key management in production

## 🚀 Production Deployment

### Environment Setup
```env
NODE_ENV=production
NEXT_PUBLIC_SUPABASE_URL=https://your-prod-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-prod-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-prod-service-role-key
ENCRYPTION_KEY=your-production-encryption-key
```

### Database Backups
- Enable automatic backups in Supabase dashboard
- Set up point-in-time recovery
- Test backup restoration procedures

### Monitoring
- Set up database monitoring alerts
- Monitor API usage and rate limits
- Track connection success/failure rates

## 🐛 Troubleshooting

### Common Issues

**"Invalid API key" errors**
- Check environment variables are loaded correctly
- Verify API keys are copied completely
- Ensure no extra spaces in keys

**RLS policy violations**
- Check user authentication status
- Verify user_id is set correctly in requests
- Review RLS policies in Supabase dashboard

**Encryption/Decryption errors**
- Verify ENCRYPTION_KEY is consistent
- Check key length (should be 32+ characters)
- Ensure key hasn't changed between encrypt/decrypt

### Debug Mode

Enable debug logging:
```env
DEBUG=true
```

Check Supabase logs in dashboard under **Logs** → **Database**.

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [Database Functions](https://supabase.com/docs/guides/database/functions)
