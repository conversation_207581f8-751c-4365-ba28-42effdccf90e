# Quick Start Guide (Without Supabase)

Get the n8n Service Integration Hub running quickly without setting up Supabase initially. The app will use mock data for development.

## 🚀 Quick Setup (5 minutes)

### 1. Install Dependencies
```bash
npm install --legacy-peer-deps
```

### 2. Start n8n Backend
```bash
npm run n8n:start
```
Wait for services to start (check with `npm run n8n:logs`)

### 3. Configure n8n API Key
1. Open n8n at http://localhost:5678
2. Login with `admin` / `admin123`
3. Go to Settings → API Keys
4. Generate new API key
5. Update `.env.local`:

```env
# n8n Configuration
N8N_HOST=http://localhost:5678
N8N_API_KEY=your-generated-api-key-here

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here

# Supabase (leave empty for mock mode)
# NEXT_PUBLIC_SUPABASE_URL=
# NEXT_PUBLIC_SUPABASE_ANON_KEY=
# SUPABASE_SERVICE_ROLE_KEY=
```

### 4. Start Frontend
```bash
npm run dev
```

### 5. Access Application
- **Frontend**: http://localhost:3000
- **n8n**: http://localhost:5678

## 🎯 What You'll See

### Mock Data Mode
When Supabase is not configured, the app shows:
- **Demo Connections**: Sample Telegram and WhatsApp connections
- **Service Templates**: Predefined service configurations
- **Full UI**: Complete interface with mock data

### Features Available
- ✅ Browse service templates
- ✅ View connection interface
- ✅ Test UI components
- ✅ Explore the dashboard
- ❌ Save real connections (requires Supabase)
- ❌ Store credentials securely (requires Supabase)

## 🔧 Testing the Interface

### 1. View Dashboard
- Go to http://localhost:3000
- See connection statistics and quick actions
- Explore the modern UI

### 2. Browse Connections
- Click "Connections" in navigation
- View mock connections (Demo Telegram Bot, Demo WhatsApp)
- See available services (Telegram, WhatsApp, Email, Slack)

### 3. Try Service Setup
- Click "Connect" on any service
- Fill out the form (won't save without Supabase)
- Test the connection flow

### 4. View Workflows
- Click "Workflows" to see n8n integration
- View existing workflows from your n8n instance

## 📈 Next Steps

### Option 1: Continue with Mock Data
Perfect for:
- UI development
- Testing components
- Demonstrating the interface
- Learning the system

### Option 2: Set Up Supabase
When ready for real data storage:
1. Follow [SUPABASE_SETUP.md](./SUPABASE_SETUP.md)
2. Configure environment variables
3. Run the SQL schema
4. Restart the application

## 🛠️ Development Commands

```bash
# Start development server
npm run dev

# Start n8n backend
npm run n8n:start

# Stop n8n backend
npm run n8n:stop

# View n8n logs
npm run n8n:logs

# Build for production
npm run build
```

## 🐛 Troubleshooting

### "Connection refused" errors
- Ensure n8n is running: `docker-compose ps`
- Check n8n logs: `npm run n8n:logs`
- Restart if needed: `npm run n8n:stop && npm run n8n:start`

### API key issues
- Verify API key is generated in n8n
- Check `.env.local` file has correct key
- Restart frontend after updating env

### Mock data not showing
- Check browser console for errors
- Verify all dependencies installed
- Try refreshing the page

## 🎯 Key Features Demonstrated

### Service Integration Focus
- **External Service Connections**: Telegram, WhatsApp, Email, Slack
- **Secure Credential Storage**: Encrypted storage (when Supabase configured)
- **Connection Testing**: Real-time validation before saving
- **Webhook Management**: Automatic webhook creation for n8n

### Modern Architecture
- **Next.js 15**: Latest React framework
- **TypeScript**: Full type safety
- **Tailwind CSS**: Modern styling
- **Supabase Ready**: Database integration prepared
- **n8n Integration**: Workflow automation backend

This setup gives you a fully functional interface to explore and develop with, while keeping the option to add secure data storage later!
