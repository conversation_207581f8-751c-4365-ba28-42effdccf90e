export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          display_name: string | null;
          avatar_url: string | null;
          preferences: <PERSON><PERSON> | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          display_name?: string | null;
          avatar_url?: string | null;
          preferences?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          display_name?: string | null;
          avatar_url?: string | null;
          preferences?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      service_connections: {
        Row: {
          id: string;
          user_id: string;
          service_type: string;
          service_name: string;
          encrypted_credentials: string;
          configuration: Json | null;
          status: "connected" | "disconnected" | "error";
          last_tested_at: string | null;
          error_message: string | null;
          webhook_url: string | null;
          webhook_path: string | null;
          n8n_workflow_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          service_type: string;
          service_name: string;
          encrypted_credentials: string;
          configuration?: Json | null;
          status?: "connected" | "disconnected" | "error";
          last_tested_at?: string | null;
          error_message?: string | null;
          webhook_url?: string | null;
          webhook_path?: string | null;
          n8n_workflow_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          service_type?: string;
          service_name?: string;
          encrypted_credentials?: string;
          configuration?: Json | null;
          status?: "connected" | "disconnected" | "error";
          last_tested_at?: string | null;
          error_message?: string | null;
          webhook_url?: string | null;
          webhook_path?: string | null;
          n8n_workflow_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      service_templates: {
        Row: {
          id: string;
          service_type: string;
          service_name: string;
          description: string;
          icon: string;
          auth_type: string;
          config_fields: Json;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          service_type: string;
          service_name: string;
          description: string;
          icon: string;
          auth_type: string;
          config_fields: Json;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          service_type?: string;
          service_name?: string;
          description?: string;
          icon?: string;
          auth_type?: string;
          config_fields?: Json;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      connection_logs: {
        Row: {
          id: string;
          connection_id: string;
          user_id: string;
          action: string;
          status: "success" | "error";
          message: string | null;
          details: Json | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          connection_id: string;
          user_id: string;
          action: string;
          status: "success" | "error";
          message?: string | null;
          details?: Json | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          connection_id?: string;
          user_id?: string;
          action?: string;
          status?: "success" | "error";
          message?: string | null;
          details?: Json | null;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}

// Helper types for easier usage
export type User = Database["public"]["Tables"]["users"]["Row"];
export type UserInsert = Database["public"]["Tables"]["users"]["Insert"];
export type UserUpdate = Database["public"]["Tables"]["users"]["Update"];

export type ServiceConnection =
  Database["public"]["Tables"]["service_connections"]["Row"];
export type ServiceConnectionInsert =
  Database["public"]["Tables"]["service_connections"]["Insert"];
export type ServiceConnectionUpdate =
  Database["public"]["Tables"]["service_connections"]["Update"];

export type ServiceTemplate =
  Database["public"]["Tables"]["service_templates"]["Row"];
export type ServiceTemplateInsert =
  Database["public"]["Tables"]["service_templates"]["Insert"];
export type ServiceTemplateUpdate =
  Database["public"]["Tables"]["service_templates"]["Update"];

export type ConnectionLog =
  Database["public"]["Tables"]["connection_logs"]["Row"];
export type ConnectionLogInsert =
  Database["public"]["Tables"]["connection_logs"]["Insert"];
export type ConnectionLogUpdate =
  Database["public"]["Tables"]["connection_logs"]["Update"];
