export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          display_name: string | null;
          avatar_url: string | null;
          preferences: <PERSON><PERSON> | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          display_name?: string | null;
          avatar_url?: string | null;
          preferences?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          display_name?: string | null;
          avatar_url?: string | null;
          preferences?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      service_connections: {
        Row: {
          id: string;
          user_id: string;
          service_type: string;
          service_name: string;
          encrypted_credentials: string;
          configuration: Json | null;
          status: "connected" | "disconnected" | "error";
          last_tested_at: string | null;
          error_message: string | null;
          webhook_url: string | null;
          webhook_path: string | null;
          n8n_workflow_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          service_type: string;
          service_name: string;
          encrypted_credentials: string;
          configuration?: Json | null;
          status?: "connected" | "disconnected" | "error";
          last_tested_at?: string | null;
          error_message?: string | null;
          webhook_url?: string | null;
          webhook_path?: string | null;
          n8n_workflow_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          service_type?: string;
          service_name?: string;
          encrypted_credentials?: string;
          configuration?: Json | null;
          status?: "connected" | "disconnected" | "error";
          last_tested_at?: string | null;
          error_message?: string | null;
          webhook_url?: string | null;
          webhook_path?: string | null;
          n8n_workflow_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      service_templates: {
        Row: {
          id: string;
          service_type: string;
          service_name: string;
          description: string;
          icon: string;
          auth_type: string;
          config_fields: Json;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          service_type: string;
          service_name: string;
          description: string;
          icon: string;
          auth_type: string;
          config_fields: Json;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          service_type?: string;
          service_name?: string;
          description?: string;
          icon?: string;
          auth_type?: string;
          config_fields?: Json;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      connection_logs: {
        Row: {
          id: string;
          connection_id: string;
          user_id: string;
          action: string;
          status: "success" | "error";
          message: string | null;
          details: Json | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          connection_id: string;
          user_id: string;
          action: string;
          status: "success" | "error";
          message?: string | null;
          details?: Json | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          connection_id?: string;
          user_id?: string;
          action?: string;
          status?: "success" | "error";
          message?: string | null;
          details?: Json | null;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}

// Helper types for easier usage
export type User = Database["public"]["Tables"]["users"]["Row"];
export type UserInsert = Database["public"]["Tables"]["users"]["Insert"];
export type UserUpdate = Database["public"]["Tables"]["users"]["Update"];

export type ServiceConnection =
  Database["public"]["Tables"]["service_connections"]["Row"];
export type ServiceConnectionInsert =
  Database["public"]["Tables"]["service_connections"]["Insert"];
export type ServiceConnectionUpdate =
  Database["public"]["Tables"]["service_connections"]["Update"];

export type ServiceTemplate =
  Database["public"]["Tables"]["service_templates"]["Row"];
export type ServiceTemplateInsert =
  Database["public"]["Tables"]["service_templates"]["Insert"];
export type ServiceTemplateUpdate =
  Database["public"]["Tables"]["service_templates"]["Update"];

export type ConnectionLog =
  Database["public"]["Tables"]["connection_logs"]["Row"];
export type ConnectionLogInsert =
  Database["public"]["Tables"]["connection_logs"]["Insert"];
export type ConnectionLogUpdate =
  Database["public"]["Tables"]["connection_logs"]["Update"];

// SaaS Platform Types (add these after running the migrations)
// Note: These will need to be properly generated once all tables exist
export type WorkflowCategory = {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  icon: string | null;
  color: string | null;
  sort_order: number;
  is_active: boolean;
  created_at: string;
};

export type WorkflowTemplate = {
  id: string;
  name: string;
  slug: string;
  description: string;
  long_description: string | null;
  category: string;
  tags: string[];
  icon: string;
  featured_image: string | null;
  demo_video_url: string | null;
  n8n_workflow_json: Json;
  required_services: string[];
  difficulty_level: "beginner" | "intermediate" | "advanced";
  estimated_setup_time: number;
  pricing_tier: "free" | "starter" | "pro" | "enterprise";
  is_featured: boolean;
  is_active: boolean;
  view_count: number;
  install_count: number;
  rating: number;
  created_at: string;
  updated_at: string;
};

export type SubscriptionPlan = {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  price_monthly: number;
  price_yearly: number | null;
  stripe_price_id_monthly: string | null;
  stripe_price_id_yearly: string | null;
  max_workflows: number | null;
  max_connections: number | null;
  max_executions_per_month: number | null;
  features: Json;
  is_popular: boolean;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
};

export type UserSubscription = {
  id: string;
  user_id: string;
  plan_id: string;
  stripe_subscription_id: string | null;
  stripe_customer_id: string | null;
  status: "active" | "canceled" | "past_due" | "unpaid" | "trialing";
  current_period_start: string | null;
  current_period_end: string | null;
  cancel_at_period_end: boolean;
  billing_cycle: "monthly" | "yearly";
  workflow_count: number;
  connection_count: number;
  executions_this_month: number;
  created_at: string;
  updated_at: string;
};

export type UserWorkflow = {
  id: string;
  user_id: string;
  template_id: string;
  n8n_workflow_id: string | null;
  custom_name: string | null;
  is_active: boolean;
  configuration: Json;
  last_execution_at: string | null;
  execution_count: number;
  created_at: string;
  updated_at: string;
};

// Admin Interface Types
export type AdminActivityLog = {
  id: string;
  admin_user_id: string;
  action: string;
  resource_type: string;
  resource_id: string | null;
  details: Json;
  ip_address: string | null;
  user_agent: string | null;
  created_at: string;
};

export type SystemSetting = {
  id: string;
  key: string;
  value: Json;
  description: string | null;
  is_public: boolean;
  category: string;
  updated_by: string | null;
  created_at: string;
  updated_at: string;
};

export type FeatureFlag = {
  id: string;
  name: string;
  description: string | null;
  is_enabled: boolean;
  target_percentage: number;
  target_user_segments: Json;
  conditions: Json;
  created_by: string | null;
  created_at: string;
  updated_at: string;
};

export type ContentPage = {
  id: string;
  slug: string;
  title: string;
  content: Json;
  meta_title: string | null;
  meta_description: string | null;
  is_published: boolean;
  published_at: string | null;
  created_by: string | null;
  updated_by: string | null;
  created_at: string;
  updated_at: string;
};

export type BlogPost = {
  id: string;
  title: string;
  slug: string;
  excerpt: string | null;
  content: string;
  featured_image: string | null;
  tags: string[];
  category: string | null;
  is_published: boolean;
  published_at: string | null;
  view_count: number;
  author_id: string | null;
  created_at: string;
  updated_at: string;
};

export type EmailTemplate = {
  id: string;
  name: string;
  slug: string;
  subject: string;
  html_content: string;
  text_content: string | null;
  template_variables: Json;
  category: string;
  is_active: boolean;
  created_by: string | null;
  updated_by: string | null;
  created_at: string;
  updated_at: string;
};

export type SupportTicket = {
  id: string;
  ticket_number: string;
  user_id: string | null;
  subject: string;
  description: string;
  status: "open" | "in_progress" | "waiting" | "resolved" | "closed";
  priority: "low" | "medium" | "high" | "urgent";
  category: string | null;
  assigned_to: string | null;
  resolved_at: string | null;
  created_at: string;
  updated_at: string;
};

export type SupportTicketMessage = {
  id: string;
  ticket_id: string;
  user_id: string | null;
  message: string;
  is_internal: boolean;
  attachments: Json;
  created_at: string;
};

export type AnalyticsEvent = {
  id: string;
  user_id: string | null;
  session_id: string | null;
  event_name: string;
  event_properties: Json;
  page_url: string | null;
  referrer: string | null;
  user_agent: string | null;
  ip_address: string | null;
  created_at: string;
};

export type UsageTracking = {
  id: string;
  user_id: string;
  workflow_id: string | null;
  event_type: string;
  event_data: Json;
  timestamp: string;
};
