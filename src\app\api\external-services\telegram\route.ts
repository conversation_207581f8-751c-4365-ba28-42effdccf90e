import { NextRequest, NextResponse } from "next/server";
import { getWebhookManager } from "@/lib/webhook-manager";
import { getDatabaseService } from "@/lib/database-service";
import {
  ApiResponse,
  TelegramConfig,
  TelegramWebhookInfo,
  ConnectionTestResult,
} from "@/types/n8n";

const webhookManager = getWebhookManager();
const databaseService = getDatabaseService();

export async function POST(request: NextRequest) {
  try {
    const { action, ...data } = await request.json();

    switch (action) {
      case "test_connection":
        return await testTelegramConnection(data);
      case "create_webhook":
        return await createTelegramWebhook(data);
      case "set_webhook":
        return await setTelegramWebhook(data);
      case "get_webhook_info":
        return await getTelegramWebhookInfo(data);
      case "delete_webhook":
        return await deleteTelegramWebhook(data);
      default:
        const response: ApiResponse = {
          success: false,
          error: "Invalid action",
        };
        return NextResponse.json(response, { status: 400 });
    }
  } catch (error: any) {
    console.error("Telegram API error:", error);
    const response: ApiResponse = {
      success: false,
      error: error.message || "Failed to process Telegram request",
    };
    return NextResponse.json(response, { status: 500 });
  }
}

async function testTelegramConnection(
  config: TelegramConfig
): Promise<NextResponse> {
  try {
    const { botToken } = config;

    if (!botToken) {
      const response: ApiResponse = {
        success: false,
        error: "Bot token is required",
      };
      return NextResponse.json(response, { status: 400 });
    }

    // Test the bot token by calling getMe
    const telegramResponse = await fetch(
      `https://api.telegram.org/bot${botToken}/getMe`
    );
    const result = await telegramResponse.json();

    if (!result.ok) {
      const testResult: ConnectionTestResult = {
        success: false,
        message: result.description || "Invalid bot token",
        timestamp: new Date().toISOString(),
      };

      const response: ApiResponse<ConnectionTestResult> = {
        success: true,
        data: testResult,
      };
      return NextResponse.json(response);
    }

    const testResult: ConnectionTestResult = {
      success: true,
      message: `Connected successfully to bot: ${result.result.first_name} (@${result.result.username})`,
      details: {
        botId: result.result.id,
        botName: result.result.first_name,
        botUsername: result.result.username,
        canJoinGroups: result.result.can_join_groups,
        canReadAllGroupMessages: result.result.can_read_all_group_messages,
        supportsInlineQueries: result.result.supports_inline_queries,
      },
      timestamp: new Date().toISOString(),
    };

    const response: ApiResponse<ConnectionTestResult> = {
      success: true,
      data: testResult,
    };
    return NextResponse.json(response);
  } catch (error: any) {
    const testResult: ConnectionTestResult = {
      success: false,
      message: error.message || "Connection test failed",
      timestamp: new Date().toISOString(),
    };

    const response: ApiResponse<ConnectionTestResult> = {
      success: true,
      data: testResult,
    };
    return NextResponse.json(response);
  }
}

async function createTelegramWebhook(data: {
  botName: string;
  botToken: string;
  userId?: string;
}): Promise<NextResponse> {
  try {
    const { botName, botToken, userId } = data;

    // Create the webhook workflow in n8n
    const workflow = await webhookManager.createTelegramWebhook(botName);
    const webhookPath = workflow.nodes.find(
      (n) => n.type === "n8n-nodes-base.webhook"
    )?.parameters.path;
    const webhookUrl = webhookManager.getWebhookUrl(
      workflow.id,
      webhookPath || ""
    );

    // Store connection securely in Supabase
    const connection = await databaseService.createServiceConnection({
      user_id: userId || null,
      service_type: "telegram",
      service_name: botName,
      credentials: {
        botToken,
        botName,
      },
      configuration: {
        webhookPath,
        workflowId: workflow.id,
      },
      status: "connected",
      webhook_url: webhookUrl,
      webhook_path: webhookPath,
      n8n_workflow_id: workflow.id,
    });

    const response: ApiResponse = {
      success: true,
      data: {
        connection,
        workflow,
        webhookPath,
        webhookUrl,
      },
      message: "Telegram webhook created successfully",
    };
    return NextResponse.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message || "Failed to create Telegram webhook",
    };
    return NextResponse.json(response, { status: 500 });
  }
}

async function setTelegramWebhook(data: {
  botToken: string;
  webhookUrl: string;
}): Promise<NextResponse> {
  try {
    const { botToken, webhookUrl } = data;

    const telegramResponse = await fetch(
      `https://api.telegram.org/bot${botToken}/setWebhook`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: webhookUrl,
          allowed_updates: ["message", "callback_query"],
        }),
      }
    );

    const result = await telegramResponse.json();

    const response: ApiResponse = {
      success: result.ok,
      data: result,
      message: result.ok ? "Webhook set successfully" : result.description,
    };
    return NextResponse.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message || "Failed to set Telegram webhook",
    };
    return NextResponse.json(response, { status: 500 });
  }
}

async function getTelegramWebhookInfo(data: {
  botToken: string;
}): Promise<NextResponse> {
  try {
    const { botToken } = data;

    const telegramResponse = await fetch(
      `https://api.telegram.org/bot${botToken}/getWebhookInfo`
    );
    const result = await telegramResponse.json();

    if (!result.ok) {
      const response: ApiResponse = {
        success: false,
        error: result.description || "Failed to get webhook info",
      };
      return NextResponse.json(response, { status: 400 });
    }

    const webhookInfo: TelegramWebhookInfo = {
      url: result.result.url,
      hasCustomCertificate: result.result.has_custom_certificate,
      pendingUpdateCount: result.result.pending_update_count,
      lastErrorDate: result.result.last_error_date,
      lastErrorMessage: result.result.last_error_message,
    };

    const response: ApiResponse<TelegramWebhookInfo> = {
      success: true,
      data: webhookInfo,
    };
    return NextResponse.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message || "Failed to get Telegram webhook info",
    };
    return NextResponse.json(response, { status: 500 });
  }
}

async function deleteTelegramWebhook(data: {
  botToken: string;
}): Promise<NextResponse> {
  try {
    const { botToken } = data;

    const telegramResponse = await fetch(
      `https://api.telegram.org/bot${botToken}/deleteWebhook`,
      {
        method: "POST",
      }
    );

    const result = await telegramResponse.json();

    const response: ApiResponse = {
      success: result.ok,
      data: result,
      message: result.ok ? "Webhook deleted successfully" : result.description,
    };
    return NextResponse.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message || "Failed to delete Telegram webhook",
    };
    return NextResponse.json(response, { status: 500 });
  }
}
