export default function HomePage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="py-20 text-center">
        <h1 className="text-4xl font-bold text-gray-900 sm:text-6xl">
          Automate Your Workflows
        </h1>
        <p className="mt-6 text-lg leading-8 text-gray-600 max-w-2xl mx-auto">
          Connect your favorite services to powerful n8n automation workflows. 
          Streamline your business processes with our secure integration platform.
        </p>
        <div className="mt-10 flex items-center justify-center gap-x-6">
          <a
            href="/auth/signup"
            className="rounded-md bg-blue-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
          >
            Get started for free
          </a>
          <a
            href="/workflows"
            className="text-sm font-semibold leading-6 text-gray-900"
          >
            Browse workflows <span aria-hidden="true">→</span>
          </a>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20">
        <div className="text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Everything you need to automate
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Powerful features to connect, automate, and scale your business processes.
          </p>
        </div>
        
        <div className="mt-20 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 rounded-lg bg-blue-100 flex items-center justify-center">
              <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
              </svg>
            </div>
            <h3 className="mt-6 text-lg font-semibold text-gray-900">Easy Integration</h3>
            <p className="mt-2 text-gray-600">
              Connect popular services like Telegram, WhatsApp, Slack, and more with just a few clicks.
            </p>
          </div>

          <div className="text-center">
            <div className="mx-auto h-12 w-12 rounded-lg bg-green-100 flex items-center justify-center">
              <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
              </svg>
            </div>
            <h3 className="mt-6 text-lg font-semibold text-gray-900">Secure & Reliable</h3>
            <p className="mt-2 text-gray-600">
              Your data is encrypted and secure. Built with enterprise-grade security and reliability.
            </p>
          </div>

          <div className="text-center">
            <div className="mx-auto h-12 w-12 rounded-lg bg-purple-100 flex items-center justify-center">
              <svg className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
              </svg>
            </div>
            <h3 className="mt-6 text-lg font-semibold text-gray-900">Powerful Automation</h3>
            <p className="mt-2 text-gray-600">
              Leverage the full power of n8n workflows to automate complex business processes.
            </p>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20">
        <div className="rounded-2xl bg-gray-50 px-6 py-16 text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900">
            Ready to get started?
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Join thousands of users who are already automating their workflows.
          </p>
          <div className="mt-10">
            <a
              href="/auth/signup"
              className="rounded-md bg-blue-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
            >
              Start automating today
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
