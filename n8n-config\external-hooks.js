/**
 * External hooks for n8n integration
 * This file contains hooks that can be used to extend n8n functionality
 */

module.exports = {
  // Hook that runs when a workflow execution starts
  'workflow.preExecute': [
    async function(workflowData, mode) {
      console.log(`Workflow ${workflowData.name} (${workflowData.id}) started in ${mode} mode`);
    }
  ],

  // Hook that runs when a workflow execution completes
  'workflow.postExecute': [
    async function(workflowData, runData, mode) {
      console.log(`Workflow ${workflowData.name} (${workflowData.id}) completed in ${mode} mode`);
      
      // You can add custom logic here, such as:
      // - Sending notifications
      // - Logging to external systems
      // - Updating databases
      // - Triggering other workflows
    }
  ],

  // Hook that runs when a node execution starts
  'node.preExecute': [
    async function(nodeName, node, workflowData, runData, mode) {
      // Log node execution for debugging
      if (process.env.DEBUG === 'true') {
        console.log(`Executing node: ${nodeName} in workflow: ${workflowData.name}`);
      }
    }
  ],

  // Hook that runs when a node execution completes
  'node.postExecute': [
    async function(nodeN<PERSON>, node, workflowData, runData, mode) {
      // Log node completion for debugging
      if (process.env.DEBUG === 'true') {
        console.log(`Completed node: ${nodeName} in workflow: ${workflowData.name}`);
      }
    }
  ],

  // Hook that runs when a webhook is received
  'webhook.received': [
    async function(webhookData, workflowData) {
      console.log(`Webhook received for workflow: ${workflowData.name}`);
      
      // You can add webhook-specific logic here
      // - Validate webhook signatures
      // - Log webhook data
      // - Transform incoming data
    }
  ]
};
