-- Create users table to extend Supabase auth.users
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY,
    display_name VARCHAR(100),
    avatar_url TEXT,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create service_templates table
CREATE TABLE IF NOT EXISTS public.service_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    service_type VARCHAR(50) NOT NULL,
    service_name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    icon VARCHAR(50) NOT NULL,
    auth_type VARCHAR(50) NOT NULL,
    config_fields JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create service_connections table
CREATE TABLE IF NOT EXISTS public.service_connections (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    service_type VARCHAR(50) NOT NULL,
    service_name VARCHAR(100) NOT NULL,
    encrypted_credentials TEXT NOT NULL,
    configuration JSONB,
    status VARCHAR(20) DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error')),
    last_tested_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    webhook_url TEXT,
    webhook_path VARCHAR(255),
    n8n_workflow_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create connection_logs table for audit trail
CREATE TABLE IF NOT EXISTS public.connection_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    connection_id UUID REFERENCES public.service_connections(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'error')),
    message TEXT,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_created_at ON public.users(created_at);
CREATE INDEX IF NOT EXISTS idx_service_connections_user_id ON public.service_connections(user_id);
CREATE INDEX IF NOT EXISTS idx_service_connections_service_type ON public.service_connections(service_type);
CREATE INDEX IF NOT EXISTS idx_service_connections_status ON public.service_connections(status);
CREATE INDEX IF NOT EXISTS idx_connection_logs_connection_id ON public.connection_logs(connection_id);
CREATE INDEX IF NOT EXISTS idx_connection_logs_user_id ON public.connection_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_connection_logs_created_at ON public.connection_logs(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.connection_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_templates ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- RLS Policies for service_connections table
CREATE POLICY "Users can view their own connections" ON public.service_connections
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own connections" ON public.service_connections
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own connections" ON public.service_connections
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own connections" ON public.service_connections
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for connection_logs table
CREATE POLICY "Users can view their own connection logs" ON public.connection_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own connection logs" ON public.connection_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for service_templates table (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view service templates" ON public.service_templates
    FOR SELECT USING (auth.role() = 'authenticated');

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_connections_updated_at
    BEFORE UPDATE ON public.service_connections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_templates_updated_at
    BEFORE UPDATE ON public.service_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically create user profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, display_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user profile
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Add foreign key constraints after tables are created
-- Note: In Supabase, we need to reference auth.users differently
ALTER TABLE public.users
ADD CONSTRAINT users_id_fkey
FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE public.service_connections
ADD CONSTRAINT service_connections_user_id_fkey
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE public.connection_logs
ADD CONSTRAINT connection_logs_user_id_fkey
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Insert default service templates
INSERT INTO public.service_templates (service_type, service_name, description, icon, auth_type, config_fields) VALUES
('telegram', 'Telegram', 'Connect your Telegram bot to receive and send messages', 'MessageSquare', 'api_key', '[
    {
        "name": "botToken",
        "label": "Bot Token",
        "type": "password",
        "required": true,
        "placeholder": "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz",
        "description": "Get this from @BotFather on Telegram"
    },
    {
        "name": "botName",
        "label": "Bot Name",
        "type": "text",
        "required": true,
        "placeholder": "My Awesome Bot",
        "description": "A friendly name for your bot"
    }
]'),
('whatsapp', 'WhatsApp Business', 'Connect WhatsApp Business API for messaging automation', 'Phone', 'bearer_token', '[
    {
        "name": "accessToken",
        "label": "Access Token",
        "type": "password",
        "required": true,
        "placeholder": "EAAxxxxxxxxxxxxxxx",
        "description": "Get this from Facebook Developer Console"
    },
    {
        "name": "phoneNumberId",
        "label": "Phone Number ID",
        "type": "text",
        "required": true,
        "placeholder": "1234567890123456",
        "description": "Your WhatsApp Business phone number ID"
    },
    {
        "name": "businessName",
        "label": "Business Name",
        "type": "text",
        "required": true,
        "placeholder": "My Business",
        "description": "Name of your business"
    }
]'),
('email', 'Email (SMTP)', 'Send automated emails via SMTP', 'Mail', 'basic_auth', '[
    {
        "name": "host",
        "label": "SMTP Host",
        "type": "text",
        "required": true,
        "placeholder": "smtp.gmail.com",
        "description": "Your SMTP server hostname"
    },
    {
        "name": "port",
        "label": "Port",
        "type": "text",
        "required": true,
        "placeholder": "587",
        "description": "SMTP port (usually 587 or 465)"
    },
    {
        "name": "username",
        "label": "Username",
        "type": "text",
        "required": true,
        "placeholder": "<EMAIL>",
        "description": "Your email address"
    },
    {
        "name": "password",
        "label": "Password",
        "type": "password",
        "required": true,
        "placeholder": "Enter your password or app password",
        "description": "Your email password or app-specific password"
    }
]'),
('slack', 'Slack', 'Send messages and notifications to Slack channels', 'Slack', 'oauth', '[
    {
        "name": "webhookUrl",
        "label": "Webhook URL",
        "type": "url",
        "required": true,
        "placeholder": "https://hooks.slack.com/services/...",
        "description": "Your Slack incoming webhook URL"
    },
    {
        "name": "channel",
        "label": "Default Channel",
        "type": "text",
        "required": false,
        "placeholder": "#general",
        "description": "Default channel for messages"
    }
]')
ON CONFLICT DO NOTHING;
