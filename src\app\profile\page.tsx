"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { useAuth } from "@/lib/auth-context";
import { Loader2, Save } from "lucide-react";

const profileSchema = z.object({
  display_name: z.string().min(1, "Display name is required"),
  avatar_url: z
    .string()
    .url("Must be a valid URL")
    .optional()
    .or(z.literal("")),
});

type ProfileFormData = z.infer<typeof profileSchema>;

function ProfileContent() {
  const { user, userProfile, updateProfile } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      display_name: userProfile?.display_name || "",
      avatar_url: userProfile?.avatar_url || "",
    },
  });

  const onSubmit = async (data: ProfileFormData) => {
    setIsLoading(true);
    setMessage(null);

    try {
      const result = await updateProfile({
        display_name: data.display_name,
        avatar_url: data.avatar_url || null,
      });

      if (result.error) {
        setMessage({
          type: "error",
          text: result.error.message || "Failed to update profile",
        });
      } else {
        setMessage({ type: "success", text: "Profile updated successfully!" });
      }
    } catch (error: any) {
      setMessage({
        type: "error",
        text: error.message || "An unexpected error occurred",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const displayName =
    userProfile?.display_name || user?.email?.split("@")[0] || "User";
  const avatarUrl = userProfile?.avatar_url;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Profile Settings
              </h1>
              <p className="text-gray-600 mt-1">
                Manage your personal information and preferences
              </p>
            </div>
          </div>
        </div>
        <div className="content-card">
          <div className="flex items-center space-x-4 mb-8">
            <Avatar className="h-16 w-16">
              <AvatarImage src={avatarUrl || undefined} alt={displayName} />
              <AvatarFallback className="text-lg bg-blue-100 text-blue-600">
                {displayName.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Profile Information
              </h2>
              <p className="text-gray-600">
                Update your personal information and preferences
              </p>
            </div>
          </div>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-2">
              <Label
                htmlFor="email"
                className="text-sm font-medium text-gray-700"
              >
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                value={user?.email || ""}
                disabled
                className="bg-gray-50 border-gray-300"
              />
              <p className="text-sm text-gray-500">
                Email address cannot be changed
              </p>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="display_name"
                className="text-sm font-medium text-gray-700"
              >
                Display Name
              </Label>
              <Input
                id="display_name"
                type="text"
                placeholder="Enter your display name"
                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                {...register("display_name")}
              />
              {errors.display_name && (
                <p className="text-sm text-red-600">
                  {errors.display_name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="avatar_url"
                className="text-sm font-medium text-gray-700"
              >
                Avatar URL (Optional)
              </Label>
              <Input
                id="avatar_url"
                type="url"
                placeholder="https://example.com/avatar.jpg"
                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                {...register("avatar_url")}
              />
              {errors.avatar_url && (
                <p className="text-sm text-red-600">
                  {errors.avatar_url.message}
                </p>
              )}
              <p className="text-sm text-gray-500">
                Enter a URL to your profile picture
              </p>
            </div>

            {message && (
              <div
                className={`p-4 text-sm rounded-lg ${
                  message.type === "success"
                    ? "text-green-700 bg-green-50 border border-green-200"
                    : "text-red-700 bg-red-50 border border-red-200"
                }`}
              >
                {message.text}
              </div>
            )}

            <div className="flex justify-end space-x-4">
              <Link href="/">
                <button
                  type="button"
                  className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
              </Link>
              <button
                type="submit"
                className="btn-primary flex items-center space-x-2"
                disabled={isLoading}
              >
                {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
                <Save className="w-4 h-4" />
                <span>Save Changes</span>
              </button>
            </div>
          </form>
        </div>

        {/* Account Information */}
        <div className="content-card mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Account Information
          </h3>
          <p className="text-gray-600 mb-6">
            View your account details and statistics
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label className="text-sm font-medium text-gray-500">
                User ID
              </Label>
              <p className="text-sm font-mono bg-gray-50 p-3 rounded-lg mt-1 break-all">
                {user?.id}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-500">
                Account Created
              </Label>
              <p className="text-sm mt-1 p-3">
                {userProfile?.created_at
                  ? new Date(userProfile.created_at).toLocaleDateString()
                  : "N/A"}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ProfilePage() {
  return (
    <ProtectedRoute>
      <ProfileContent />
    </ProtectedRoute>
  );
}
