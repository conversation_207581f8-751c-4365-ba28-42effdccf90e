-- ============================================================================
-- COMPLETE FRESH SETUP FOR n8n SERVICE INTEGRATION HUB
-- ============================================================================
-- This script completely removes all existing tables and creates everything fresh
-- Copy and paste this ENTIRE script into your Supabase SQL Editor and run it

-- ============================================================================
-- STEP 1: CLEAN SLATE - Remove everything
-- ============================================================================

-- Drop all existing tables in correct order (dependencies first)
DROP TABLE IF EXISTS public.connection_logs CASCADE;
DROP TABLE IF EXISTS public.service_connections CASCADE;
DROP TABLE IF EXISTS public.service_templates CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;

-- Drop existing triggers and functions
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users CASCADE;
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- ============================================================================
-- STEP 2: CREATE TABLES
-- ============================================================================

-- Create users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID PRIMARY KEY,
    display_name VARCHAR(100),
    avatar_url TEXT,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create service templates table
CREATE TABLE public.service_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    service_type VARCHAR(50) NOT NULL UNIQUE,
    service_name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    icon VARCHAR(50) NOT NULL,
    auth_type VARCHAR(50) NOT NULL,
    config_fields JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create service connections table
CREATE TABLE public.service_connections (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    service_type VARCHAR(50) NOT NULL,
    service_name VARCHAR(100) NOT NULL,
    encrypted_credentials TEXT NOT NULL,
    configuration JSONB,
    status VARCHAR(20) DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error')),
    last_tested_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    webhook_url TEXT,
    webhook_path VARCHAR(255),
    n8n_workflow_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create connection logs table
CREATE TABLE public.connection_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    connection_id UUID REFERENCES public.service_connections(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'error')),
    message TEXT,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 3: CREATE INDEXES
-- ============================================================================

CREATE INDEX idx_users_created_at ON public.users(created_at);
CREATE INDEX idx_service_connections_user_id ON public.service_connections(user_id);
CREATE INDEX idx_service_connections_service_type ON public.service_connections(service_type);
CREATE INDEX idx_service_connections_status ON public.service_connections(status);
CREATE INDEX idx_connection_logs_connection_id ON public.connection_logs(connection_id);
CREATE INDEX idx_connection_logs_user_id ON public.connection_logs(user_id);
CREATE INDEX idx_connection_logs_created_at ON public.connection_logs(created_at);

-- ============================================================================
-- STEP 4: CREATE FUNCTIONS
-- ============================================================================

-- Function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, display_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- STEP 5: ENABLE ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.connection_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_templates ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 6: CREATE RLS POLICIES
-- ============================================================================

-- Users table policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Service connections policies
CREATE POLICY "Users can view their own connections" ON public.service_connections
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own connections" ON public.service_connections
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own connections" ON public.service_connections
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own connections" ON public.service_connections
    FOR DELETE USING (auth.uid() = user_id);

-- Connection logs policies
CREATE POLICY "Users can view their own connection logs" ON public.connection_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own connection logs" ON public.connection_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Service templates policies (readable by all authenticated users)
CREATE POLICY "Authenticated users can view service templates" ON public.service_templates
    FOR SELECT USING (auth.role() = 'authenticated');

-- ============================================================================
-- STEP 7: CREATE TRIGGERS
-- ============================================================================

-- Triggers for updated_at columns
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON public.users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_connections_updated_at 
    BEFORE UPDATE ON public.service_connections 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_templates_updated_at 
    BEFORE UPDATE ON public.service_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger for automatic user profile creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ============================================================================
-- STEP 8: INSERT SAMPLE DATA
-- ============================================================================

-- Insert service templates
INSERT INTO public.service_templates (service_type, service_name, description, icon, auth_type, config_fields) VALUES
('telegram', 'Telegram Bot', 'Connect your Telegram bot to receive and send messages', 'MessageSquare', 'api_key', '[{"name":"botToken","label":"Bot Token","type":"password","required":true,"placeholder":"Enter your bot token","description":"Get this from @BotFather on Telegram"},{"name":"botName","label":"Bot Name","type":"text","required":true,"placeholder":"My Awesome Bot","description":"A friendly name for your bot"}]'),
('whatsapp', 'WhatsApp Business', 'Connect WhatsApp Business API for messaging', 'Phone', 'bearer_token', '[{"name":"accessToken","label":"Access Token","type":"password","required":true,"placeholder":"Enter your WhatsApp access token","description":"Get this from Facebook Developer Console"},{"name":"phoneNumberId","label":"Phone Number ID","type":"text","required":true,"placeholder":"Enter phone number ID","description":"Your WhatsApp Business phone number ID"},{"name":"businessName","label":"Business Name","type":"text","required":true,"placeholder":"My Business","description":"Name of your business"}]'),
('email', 'Email (SMTP)', 'Send automated emails via SMTP', 'Mail', 'basic_auth', '[{"name":"host","label":"SMTP Host","type":"text","required":true,"placeholder":"smtp.gmail.com","description":"Your SMTP server hostname"},{"name":"port","label":"Port","type":"text","required":true,"placeholder":"587","description":"SMTP port (usually 587 or 465)"},{"name":"username","label":"Username","type":"text","required":true,"placeholder":"<EMAIL>","description":"Your email address"},{"name":"password","label":"Password","type":"password","required":true,"placeholder":"Enter your password or app password","description":"Your email password or app-specific password"}]'),
('slack', 'Slack', 'Send messages and notifications to Slack channels', 'Slack', 'oauth', '[{"name":"webhookUrl","label":"Webhook URL","type":"url","required":true,"placeholder":"https://hooks.slack.com/services/...","description":"Your Slack incoming webhook URL"},{"name":"channel","label":"Default Channel","type":"text","required":false,"placeholder":"#general","description":"Default channel for messages"}]');

-- ============================================================================
-- SETUP COMPLETE!
-- ============================================================================

-- Verify the setup
SELECT 'Setup completed successfully!' as status;

-- Check that all tables were created
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('users', 'service_connections', 'connection_logs', 'service_templates')
ORDER BY table_name;

-- Check that RLS is enabled (using correct system catalog)
SELECT
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('users', 'service_connections', 'connection_logs', 'service_templates')
ORDER BY tablename;

-- Check service templates were inserted
SELECT COUNT(*) as service_templates_count FROM public.service_templates;
