# Admin Interface Specification - SaaS Platform Management

## Overview

Comprehensive admin panel specification for managing the n8n Service Integration Hub SaaS platform. This interface provides administrators with complete control over users, workflows, subscriptions, content, and system operations while maintaining the existing multi-tenant security architecture.

## 1. Admin Dashboard Overview

### 1.1 Main Dashboard Layout

```
┌─────────────────────────────────────────────────────────────────┐
│                    ADMIN NAVIGATION BAR                         │
├─────────────────────────────────────────────────────────────────┤
│ Sidebar │                 Main Content Area                     │
│         │                                                       │
│ - Dashboard │ ┌─────────────────────────────────────────────┐   │
│ - Templates │ │           KPI Cards Row                     │   │
│ - Users     │ │ Total Users │ Revenue │ Active Workflows    │   │
│ - Billing   │ └─────────────────────────────────────────────┘   │
│ - Content   │                                                   │
│ - System    │ ┌─────────────────────────────────────────────┐   │
│ - Settings  │ │           Analytics Charts                  │   │
│             │ │ User Growth │ Revenue Trend │ Usage Stats   │   │
│             │ └─────────────────────────────────────────────┘   │
│             │                                                   │
│             │ ┌─────────────────────────────────────────────┐   │
│             │ │           Recent Activity Feed              │   │
│             │ │ - New user signups                          │   │
│             │ │ - Workflow installations                    │   │
│             │ │ - Subscription changes                      │   │
│             │ │ - System alerts                             │   │
│             │ └─────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 Key Performance Indicators (KPIs)

- **Total Users**: Active user count with growth percentage
- **Monthly Recurring Revenue (MRR)**: Current MRR with trend
- **Active Workflows**: Total installed workflows across all users
- **Conversion Rate**: Visitor to paid user conversion
- **Churn Rate**: Monthly subscription cancellation rate
- **Popular Templates**: Most installed workflow templates
- **Support Tickets**: Open support requests count

### 1.3 Analytics Charts

- **User Growth**: Monthly new user registrations
- **Revenue Trend**: MRR growth over time
- **Usage Statistics**: Workflow executions per month
- **Plan Distribution**: Users by subscription tier
- **Template Performance**: Installation rates by category

## 2. Workflow Template Management

### 2.1 Template Library Overview

**Route**: `/admin/templates`

```typescript
// Template list with advanced filtering
interface TemplateListView {
  filters: {
    category: string[];
    status: "active" | "draft" | "archived";
    pricingTier: "free" | "starter" | "pro" | "enterprise";
    featured: boolean;
  };
  sorting: "name" | "installs" | "rating" | "created_at";
  search: string;
}
```

**Features**:

- Grid/list view toggle
- Bulk actions (publish, unpublish, delete)
- Quick edit inline for basic fields
- Template performance metrics overlay
- Export template data

### 2.2 Template Editor

**Route**: `/admin/templates/[id]/edit` or `/admin/templates/new`

**Form Sections**:

#### Basic Information

- Template name and slug
- Short and long descriptions
- Category selection (dropdown)
- Tags (multi-select with autocomplete)
- Difficulty level (beginner/intermediate/advanced)
- Estimated setup time

#### Media Assets

- Featured image upload with preview
- Demo video URL
- Screenshot gallery
- Icon selection from library

#### Technical Configuration

- n8n workflow JSON import/export
- Required services checklist
- Pricing tier assignment
- Feature flags and restrictions

#### Publishing Controls

- Draft/Published status
- Featured template toggle
- Visibility settings
- Publication date scheduling

### 2.3 Category Management

**Route**: `/admin/templates/categories`

- Create/edit/delete categories
- Category ordering and hierarchy
- Icon and color assignment
- Usage statistics per category

### 2.4 Template Analytics

**Route**: `/admin/templates/[id]/analytics`

- Installation count over time
- User ratings and reviews
- Usage frequency by users
- Revenue attribution (for paid tiers)
- Geographic distribution of installs

## 3. User Management

### 3.1 User Directory

**Route**: `/admin/users`

**Advanced Filtering**:

```typescript
interface UserFilters {
  subscriptionPlan: string[];
  status: "active" | "inactive" | "suspended";
  signupDateRange: [Date, Date];
  lastActiveRange: [Date, Date];
  workflowCount: { min: number; max: number };
  totalRevenue: { min: number; max: number };
  signupSource: string[];
}
```

**User List Columns**:

- User info (name, email, avatar)
- Subscription plan and status
- Signup date and source
- Last active date
- Workflow count
- Total revenue contributed
- Quick actions (view, edit, suspend)

### 3.2 Individual User Profile

**Route**: `/admin/users/[id]`

**Tabs Structure**:

#### Overview Tab

- User basic information
- Account status and flags
- Subscription summary
- Key metrics (workflows, executions, revenue)

#### Subscription Tab

- Current plan details
- Billing history
- Payment methods
- Subscription actions (upgrade, downgrade, cancel)
- Usage vs. limits tracking

#### Workflows Tab

- Installed workflow templates
- Custom workflows created
- Execution history and statistics
- Performance metrics

#### Activity Tab

- Login history
- Feature usage tracking
- Support ticket history
- Audit log of admin actions

#### Support Tab

- Contact information
- Support ticket management
- Notes and flags
- Communication history

### 3.3 Bulk User Operations

- Export user data
- Send bulk communications
- Apply subscription changes
- Suspend/unsuspend accounts
- Merge duplicate accounts

## 4. Subscription & Billing Management

### 4.1 Subscription Plans Management

**Route**: `/admin/billing/plans`

**Plan Editor Features**:

- Plan name, description, and positioning
- Pricing configuration (monthly/yearly)
- Feature limits and restrictions
- Stripe integration settings
- Plan visibility and availability
- Migration paths between plans

### 4.2 Revenue Analytics

**Route**: `/admin/billing/analytics`

**Revenue Dashboards**:

- MRR and ARR tracking
- Revenue by plan breakdown
- Churn analysis and cohort retention
- Payment failure rates
- Geographic revenue distribution
- Seasonal trends and forecasting

### 4.3 Billing Operations

**Route**: `/admin/billing/operations`

**Management Tools**:

- Failed payment recovery
- Refund processing
- Subscription modifications
- Promotional code management
- Tax and compliance reporting
- Dunning management

### 4.4 Customer Success

**Route**: `/admin/billing/success`

**Features**:

- At-risk customer identification
- Upgrade opportunity tracking
- Usage-based upselling suggestions
- Customer health scores
- Retention campaign management

## 5. Content Management

### 5.1 Marketing Pages

**Route**: `/admin/content/marketing`

**Editable Content Sections**:

- Homepage hero and sections
- Pricing page content
- About page information
- Feature descriptions
- Customer testimonials
- FAQ management

### 5.2 Blog Management

**Route**: `/admin/content/blog`

**Blog Features**:

- Rich text editor for posts
- SEO optimization tools
- Category and tag management
- Publication scheduling
- Comment moderation
- Analytics integration

### 5.3 Documentation

**Route**: `/admin/content/docs`

**Documentation Tools**:

- Hierarchical page structure
- Markdown editor with preview
- Version control and history
- Search optimization
- User feedback collection
- Analytics on page usage

### 5.4 Email Templates

**Route**: `/admin/content/emails`

**Template Management**:

- Transactional email templates
- Marketing email campaigns
- Automated drip sequences
- A/B testing capabilities
- Personalization variables
- Delivery analytics

## 6. System Administration

### 6.1 Platform Health

**Route**: `/admin/system/health`

**Monitoring Dashboards**:

- System uptime and performance
- Database query performance
- API response times
- Error rates and alerts
- Resource utilization
- Third-party service status

### 6.2 Integration Management

**Route**: `/admin/system/integrations`

**Integration Controls**:

- Stripe configuration
- n8n instance management
- Email service settings
- Analytics platform setup
- Error monitoring configuration
- CDN and storage settings

### 6.3 Security & Compliance

**Route**: `/admin/system/security`

**Security Features**:

- User access logs
- Failed login attempts
- API key management
- Data export requests
- GDPR compliance tools
- Security audit logs

### 6.4 Feature Flags

**Route**: `/admin/system/features`

**Feature Management**:

- Enable/disable platform features
- A/B testing configuration
- Gradual rollout controls
- User segment targeting
- Performance impact monitoring
- Rollback capabilities

## 7. Technical Implementation

### 7.1 Admin Route Structure

```
src/app/(dashboard)/admin/
├── layout.tsx                 # Admin-only layout with navigation
├── page.tsx                   # Main dashboard
├── templates/
│   ├── page.tsx              # Template library
│   ├── new/page.tsx          # Create new template
│   ├── [id]/
│   │   ├── edit/page.tsx     # Edit template
│   │   └── analytics/page.tsx # Template analytics
│   └── categories/page.tsx    # Category management
├── users/
│   ├── page.tsx              # User directory
│   └── [id]/page.tsx         # User profile
├── billing/
│   ├── plans/page.tsx        # Subscription plans
│   ├── analytics/page.tsx    # Revenue analytics
│   └── operations/page.tsx   # Billing operations
├── content/
│   ├── marketing/page.tsx    # Marketing pages
│   ├── blog/page.tsx         # Blog management
│   └── docs/page.tsx         # Documentation
└── system/
    ├── health/page.tsx       # System monitoring
    ├── integrations/page.tsx # Integration settings
    └── security/page.tsx     # Security & compliance
```

### 7.2 Admin Access Control

#### Enhanced RLS Policies

```sql
-- Admin-only access to sensitive operations
CREATE POLICY "Admins can manage all workflow templates"
ON public.workflow_templates
FOR ALL USING (
    auth.uid() IN (
        SELECT id FROM public.users WHERE is_admin = true
    )
);

-- Admin read access to all user data
CREATE POLICY "Admins can view all user subscriptions"
ON public.user_subscriptions
FOR SELECT USING (
    auth.uid() IN (
        SELECT id FROM public.users WHERE is_admin = true
    )
);
```

#### Middleware Protection

```typescript
// src/middleware.ts
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Admin routes require admin role
  if (pathname.startsWith("/admin")) {
    return adminAuthMiddleware(request);
  }

  return NextResponse.next();
}

async function adminAuthMiddleware(request: NextRequest) {
  const user = await getUser(request);

  if (!user?.is_admin) {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  return NextResponse.next();
}
```

### 7.3 Additional Database Tables

#### Admin Activity Logs

```sql
CREATE TABLE public.admin_activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_user_id UUID NOT NULL REFERENCES public.users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100),
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### System Settings

```sql
CREATE TABLE public.system_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key VARCHAR(100) NOT NULL UNIQUE,
    value JSONB NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    updated_by UUID REFERENCES public.users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Feature Flags

```sql
CREATE TABLE public.feature_flags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_enabled BOOLEAN DEFAULT false,
    target_percentage INTEGER DEFAULT 100,
    target_user_segments JSONB DEFAULT '[]',
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 7.4 Reusable Components

#### Existing Components to Extend

- ✅ **UI Components**: All Radix UI components
- ✅ **Layout Components**: Sidebar navigation pattern
- ✅ **Data Tables**: Extend for user/template lists
- ✅ **Forms**: Reuse form components for admin forms
- ✅ **Charts**: Add analytics chart components

#### New Admin-Specific Components

```typescript
// Admin component library
src/components/admin/
├── analytics/
│   ├── kpi-card.tsx
│   ├── revenue-chart.tsx
│   ├── user-growth-chart.tsx
│   └── usage-analytics.tsx
├── templates/
│   ├── template-editor.tsx
│   ├── template-gallery.tsx
│   ├── category-manager.tsx
│   └── asset-uploader.tsx
├── users/
│   ├── user-directory.tsx
│   ├── user-profile.tsx
│   ├── subscription-manager.tsx
│   └── bulk-actions.tsx
├── billing/
│   ├── plan-editor.tsx
│   ├── revenue-dashboard.tsx
│   └── billing-operations.tsx
└── system/
    ├── health-monitor.tsx
    ├── integration-settings.tsx
    └── feature-flags.tsx
```

### 7.5 API Endpoints

#### Admin-Specific APIs

```typescript
// Admin API routes
src/app/api/admin/
├── analytics/
│   ├── overview/route.ts      # Dashboard KPIs
│   ├── users/route.ts         # User analytics
│   └── revenue/route.ts       # Revenue analytics
├── templates/
│   ├── route.ts              # Template CRUD
│   ├── [id]/route.ts         # Individual template
│   └── categories/route.ts    # Category management
├── users/
│   ├── route.ts              # User management
│   ├── [id]/route.ts         # Individual user
│   └── bulk/route.ts         # Bulk operations
├── billing/
│   ├── plans/route.ts        # Plan management
│   ├── subscriptions/route.ts # Subscription operations
│   └── analytics/route.ts     # Billing analytics
└── system/
    ├── health/route.ts       # System health
    ├── settings/route.ts     # System settings
    └── logs/route.ts         # Activity logs
```

## 8. Integration Points

### 8.1 Dashboard Integration

- Admin menu item in existing sidebar (for admin users only)
- Admin badge/indicator in user profile
- Quick admin actions in regular dashboard
- Seamless switching between user and admin views

### 8.2 Public Page Integration

- Admin preview mode for marketing pages
- Quick edit links for content (admin users only)
- A/B testing controls overlay
- Performance monitoring widgets

### 8.3 Notification System

- Real-time admin alerts
- Email notifications for critical events
- Dashboard notification center
- Mobile push notifications for urgent issues

## 9. Security Considerations

### 9.1 Access Control

- Multi-factor authentication for admin accounts
- Role-based permissions (super admin, content admin, support admin)
- Session timeout and security policies
- IP whitelisting for admin access

### 9.2 Audit Trail

- Complete audit log of all admin actions
- Data export and modification tracking
- User impersonation logging
- System change history

### 9.3 Data Protection

- Encrypted sensitive data display
- PII masking in logs and exports
- GDPR compliance tools
- Data retention policies

This comprehensive admin interface provides complete platform management capabilities while maintaining security and usability. The modular design allows for phased implementation and easy extension as the platform grows.

## 10. Implementation Priority & Phases

### Phase 1: Core Admin Foundation (Week 1)

**Priority: Critical**

- [ ] Admin route structure and layout
- [ ] Admin access control middleware
- [ ] Basic admin dashboard with KPIs
- [ ] Admin navigation and user switching
- [ ] Database migration for admin tables

### Phase 2: User & Template Management (Week 2)

**Priority: High**

- [ ] User directory and search
- [ ] Individual user profile management
- [ ] Template library management
- [ ] Template editor and publishing
- [ ] Category management

### Phase 3: Analytics & Billing (Week 3)

**Priority: High**

- [ ] Revenue analytics dashboard
- [ ] Subscription plan management
- [ ] Billing operations interface
- [ ] User subscription management
- [ ] Usage analytics and reporting

### Phase 4: Content & Support (Week 4)

**Priority: Medium**

- [ ] Marketing page content management
- [ ] Blog management system
- [ ] Support ticket system
- [ ] Email template management
- [ ] Documentation management

### Phase 5: System Administration (Week 5)

**Priority: Medium**

- [ ] System health monitoring
- [ ] Integration management
- [ ] Feature flags system
- [ ] Security and audit logs
- [ ] Performance monitoring

## 11. Quick Start Implementation Guide

### Step 1: Database Setup

```bash
# Run the admin interface migration
# Copy admin-interface-migration.sql to Supabase SQL Editor and execute
```

### Step 2: Create Admin Layout

```typescript
// src/app/(dashboard)/admin/layout.tsx
import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { AdminHeader } from "@/components/admin/admin-header";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen bg-gray-50">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-auto p-6">{children}</main>
      </div>
    </div>
  );
}
```

### Step 3: Implement Admin Middleware

```typescript
// src/middleware.ts
import { NextRequest, NextResponse } from "next/server";
import { createMiddlewareClient } from "@supabase/auth-helpers-nextjs";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  if (pathname.startsWith("/admin")) {
    const response = NextResponse.next();
    const supabase = createMiddlewareClient({ req: request, res: response });

    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.redirect(new URL("/auth/signin", request.url));
    }

    // Check if user is admin
    const { data: user } = await supabase
      .from("users")
      .select("is_admin")
      .eq("id", session.user.id)
      .single();

    if (!user?.is_admin) {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/admin/:path*"],
};
```

### Step 4: Create Admin Dashboard

```typescript
// src/app/(dashboard)/admin/page.tsx
import { AdminDashboard } from "@/components/admin/admin-dashboard";

export default function AdminPage() {
  return <AdminDashboard />;
}
```

### Step 5: Add Admin Navigation

```typescript
// Update existing sidebar to include admin link
const navigation = [
  // ... existing items
  {
    name: "Admin Panel",
    href: "/admin",
    icon: Shield,
    adminOnly: true,
  },
];
```

## 12. Component Library Structure

### Admin-Specific Components

```typescript
// src/components/admin/
├── admin-dashboard.tsx        # Main dashboard with KPIs
├── admin-sidebar.tsx         # Admin navigation
├── admin-header.tsx          # Admin header with user switching
├── analytics/
│   ├── kpi-cards.tsx         # Key performance indicators
│   ├── revenue-chart.tsx     # Revenue analytics
│   ├── user-growth-chart.tsx # User growth metrics
│   └── usage-analytics.tsx   # Platform usage stats
├── templates/
│   ├── template-manager.tsx  # Template CRUD interface
│   ├── template-editor.tsx   # Rich template editor
│   ├── category-manager.tsx  # Category management
│   └── template-analytics.tsx # Template performance
├── users/
│   ├── user-directory.tsx    # User list with filters
│   ├── user-profile.tsx      # Individual user management
│   ├── subscription-manager.tsx # User subscription controls
│   └── user-analytics.tsx    # User behavior analytics
├── billing/
│   ├── plan-manager.tsx      # Subscription plan CRUD
│   ├── revenue-dashboard.tsx # Revenue analytics
│   ├── billing-operations.tsx # Payment operations
│   └── customer-success.tsx  # Retention tools
├── content/
│   ├── page-editor.tsx       # Marketing page editor
│   ├── blog-manager.tsx      # Blog post management
│   ├── email-templates.tsx   # Email template editor
│   └── documentation.tsx     # Docs management
└── system/
    ├── health-monitor.tsx    # System health dashboard
    ├── feature-flags.tsx     # Feature flag management
    ├── integration-settings.tsx # Third-party integrations
    └── audit-logs.tsx        # Security audit interface
```

## 13. API Endpoints Structure

### Admin API Routes

```typescript
// src/app/api/admin/
├── analytics/
│   ├── overview/route.ts     # Dashboard KPIs
│   ├── users/route.ts        # User analytics
│   ├── revenue/route.ts      # Revenue metrics
│   └── templates/route.ts    # Template analytics
├── users/
│   ├── route.ts             # User CRUD operations
│   ├── [id]/route.ts        # Individual user management
│   ├── search/route.ts      # User search and filtering
│   └── bulk/route.ts        # Bulk user operations
├── templates/
│   ├── route.ts             # Template CRUD
│   ├── [id]/route.ts        # Individual template
│   ├── categories/route.ts   # Category management
│   └── analytics/route.ts    # Template performance
├── billing/
│   ├── plans/route.ts       # Subscription plan management
│   ├── subscriptions/route.ts # Subscription operations
│   ├── revenue/route.ts     # Revenue analytics
│   └── operations/route.ts   # Billing operations
├── content/
│   ├── pages/route.ts       # Marketing page management
│   ├── blog/route.ts        # Blog post management
│   ├── emails/route.ts      # Email template management
│   └── docs/route.ts        # Documentation management
└── system/
    ├── health/route.ts      # System health monitoring
    ├── settings/route.ts    # System configuration
    ├── features/route.ts    # Feature flag management
    └── logs/route.ts        # Audit log access
```

## 14. Security Implementation

### Admin Role Verification

```typescript
// src/lib/admin-auth.ts
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function requireAdmin() {
  const supabase = createServerComponentClient({ cookies });

  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    throw new Error("Authentication required");
  }

  const { data: user } = await supabase
    .from("users")
    .select("is_admin")
    .eq("id", session.user.id)
    .single();

  if (!user?.is_admin) {
    throw new Error("Admin access required");
  }

  return { user: session.user, isAdmin: true };
}
```

### Audit Logging

```typescript
// src/lib/admin-audit.ts
export async function logAdminAction(
  adminUserId: string,
  action: string,
  resourceType: string,
  resourceId?: string,
  details?: any
) {
  const supabase = createServerComponentClient({ cookies });

  await supabase.from("admin_activity_logs").insert({
    admin_user_id: adminUserId,
    action,
    resource_type: resourceType,
    resource_id: resourceId,
    details,
    ip_address: getClientIP(),
    user_agent: getUserAgent(),
  });
}
```

This comprehensive admin interface specification provides everything needed to build a powerful platform management system that integrates seamlessly with the existing SaaS architecture.
