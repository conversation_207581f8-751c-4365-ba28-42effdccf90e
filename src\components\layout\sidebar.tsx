"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Link as LinkIcon,
  Settings,
  User,
  Plus,
  LogOut,
} from "lucide-react";
import { useAuth } from "@/lib/auth-context";

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: LayoutDashboard },
  { name: "All Connections", href: "/connections", icon: LinkIcon },
  { name: "New Connection", href: "/connections/setup", icon: Plus },
  { name: "Settings", href: "/settings", icon: Settings },
];

export function Sidebar() {
  const pathname = usePathname();
  const { user, signOut } = useAuth();

  return (
    <div className="sidebar-nav flex flex-col">
      {/* Logo/Brand */}
      <div className="p-6 border-b border-slate-700">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <LinkIcon className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-white">n8n Hub</h1>
            <p className="text-xs text-slate-400">Service Integration</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`
                flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200
                ${
                  isActive
                    ? "bg-blue-600 text-white shadow-lg"
                    : "text-slate-300 hover:text-white hover:bg-slate-800"
                }
              `}
            >
              <item.icon className="w-5 h-5 mr-3" />
              {item.name}
            </Link>
          );
        })}
      </nav>

      {/* Spacer to push user profile to bottom */}
      <div className="flex-grow"></div>

      {/* User Profile Section */}
      <div className="p-4 border-t border-slate-700">
        {user && (
          <div className="space-y-3">
            {/* User Info */}
            <Link
              href="/profile"
              className="flex items-center p-3 rounded-lg hover:bg-slate-800 transition-colors"
            >
              <div className="w-8 h-8 bg-slate-600 rounded-full flex items-center justify-center mr-3">
                <User className="w-4 h-4 text-slate-300" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user.user_metadata?.display_name || user.email}
                </p>
                <p className="text-xs text-slate-400 truncate">{user.email}</p>
              </div>
            </Link>

            {/* Sign Out Button */}
            <button
              onClick={signOut}
              className="w-full flex items-center px-3 py-2 text-sm text-slate-300 hover:text-white hover:bg-red-600 rounded-lg transition-colors"
            >
              <LogOut className="w-4 h-4 mr-3" />
              Sign Out
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
