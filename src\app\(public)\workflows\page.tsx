"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Search,
  Filter,
  Star,
  Download,
  Clock,
  Users,
  ArrowRight,
  Zap,
  MessageSquare,
  ShoppingCart,
  Share2,
  Target,
  RefreshCw,
  Bell,
} from "lucide-react";
import { WorkflowTemplate, WorkflowCategory } from "@/types/database";

export default function WorkflowsPage() {
  const [workflows, setWorkflows] = useState<WorkflowTemplate[]>([]);
  const [categories, setCategories] = useState<WorkflowCategory[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      // Load categories
      const categoriesResponse = await fetch("/api/public/categories");
      const categoriesResult = await categoriesResponse.json();
      
      if (categoriesResult.success) {
        setCategories(categoriesResult.data);
      }

      // Load workflows
      const workflowsResponse = await fetch("/api/public/workflows?limit=20");
      const workflowsResult = await workflowsResponse.json();
      
      if (workflowsResult.success) {
        setWorkflows(workflowsResult.data);
      } else {
        // Mock data for demo when no templates exist yet
        setWorkflows([]);
      }
    } catch (error) {
      console.error("Failed to load data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getCategoryIcon = (categorySlug: string) => {
    const icons = {
      "customer-support": MessageSquare,
      "ecommerce": ShoppingCart,
      "social-media": Share2,
      "lead-generation": Target,
      "data-sync": RefreshCw,
      "notifications": Bell,
    };
    return icons[categorySlug as keyof typeof icons] || Zap;
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case "beginner":
        return "bg-green-100 text-green-800";
      case "intermediate":
        return "bg-yellow-100 text-yellow-800";
      case "advanced":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPricingColor = (tier: string) => {
    switch (tier) {
      case "free":
        return "bg-blue-100 text-blue-800";
      case "starter":
        return "bg-purple-100 text-purple-800";
      case "pro":
        return "bg-orange-100 text-orange-800";
      case "enterprise":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const filteredWorkflows = workflows.filter((workflow) => {
    const matchesSearch = 
      workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      workflow.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      workflow.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = !selectedCategory || workflow.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Workflow Templates
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Discover pre-built automation workflows to streamline your business processes. 
          Connect your services and start automating in minutes.
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search workflows..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 bg-white text-sm"
            >
              <option value="">All Categories</option>
              {categories.map((category) => (
                <option key={category.id} value={category.slug}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Category Pills */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory("")}
            className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
              !selectedCategory
                ? "bg-blue-100 text-blue-800"
                : "bg-gray-100 text-gray-600 hover:bg-gray-200"
            }`}
          >
            All
          </button>
          {categories.map((category) => {
            const IconComponent = getCategoryIcon(category.slug);
            return (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.slug)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors flex items-center space-x-1 ${
                  selectedCategory === category.slug
                    ? "bg-blue-100 text-blue-800"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                <IconComponent className="h-3 w-3" />
                <span>{category.name}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Workflows Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredWorkflows.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWorkflows.map((workflow) => {
            const IconComponent = getCategoryIcon(workflow.category);
            return (
              <Card
                key={workflow.id}
                className="hover:shadow-lg transition-shadow cursor-pointer group"
              >
                <Link href={`/workflows/${workflow.slug}`}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between mb-2">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <IconComponent className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600">
                          {workflow.rating.toFixed(1)}
                        </span>
                      </div>
                    </div>
                    <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
                      {workflow.name}
                    </CardTitle>
                    <CardDescription className="line-clamp-2">
                      {workflow.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      {/* Badges */}
                      <div className="flex flex-wrap gap-2">
                        <Badge
                          className={getDifficultyColor(workflow.difficulty_level)}
                        >
                          {workflow.difficulty_level}
                        </Badge>
                        <Badge className={getPricingColor(workflow.pricing_tier)}>
                          {workflow.pricing_tier}
                        </Badge>
                        {workflow.is_featured && (
                          <Badge className="bg-yellow-100 text-yellow-800">
                            Featured
                          </Badge>
                        )}
                      </div>

                      {/* Stats */}
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Download className="h-3 w-3" />
                          <span>{workflow.install_count}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>{workflow.estimated_setup_time}min</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="h-3 w-3" />
                          <span>{workflow.view_count}</span>
                        </div>
                      </div>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-1">
                        {workflow.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag}
                            className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                          >
                            {tag}
                          </span>
                        ))}
                        {workflow.tags.length > 3 && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                            +{workflow.tags.length - 3}
                          </span>
                        )}
                      </div>

                      {/* CTA */}
                      <div className="pt-2">
                        <Button className="w-full group-hover:bg-blue-700 transition-colors">
                          View Details
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Link>
              </Card>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-16">
          <Zap className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {searchTerm || selectedCategory
              ? "No workflows found"
              : "No workflow templates available yet"}
          </h3>
          <p className="text-gray-600 mb-8 max-w-md mx-auto">
            {searchTerm || selectedCategory
              ? "Try adjusting your search or filter criteria."
              : "We're working on adding amazing workflow templates. Check back soon!"}
          </p>
          {(searchTerm || selectedCategory) && (
            <Button
              onClick={() => {
                setSearchTerm("");
                setSelectedCategory("");
              }}
              variant="outline"
            >
              Clear Filters
            </Button>
          )}
        </div>
      )}

      {/* CTA Section */}
      <div className="mt-16 text-center">
        <div className="bg-blue-50 rounded-2xl px-8 py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to start automating?
          </h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Sign up for free and start using these workflow templates to automate 
            your business processes today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/signup">
              <Button size="lg" className="px-8">
                Get Started Free
              </Button>
            </Link>
            <Link href="/pricing">
              <Button size="lg" variant="outline" className="px-8">
                View Pricing
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
