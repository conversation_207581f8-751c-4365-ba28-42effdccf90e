import { NextRequest, NextResponse } from 'next/server';
import { getN8nClient } from '@/lib/n8n-client';
import { ApiResponse, N8nWorkflow } from '@/types/n8n';

const n8nClient = getN8nClient();

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const workflowId = params.id;
    
    if (!workflowId) {
      const response: ApiResponse = {
        success: false,
        error: 'Workflow ID is required',
      };
      return NextResponse.json(response, { status: 400 });
    }

    const workflow = await n8nClient.activateWorkflow(workflowId);
    
    const response: ApiResponse<N8nWorkflow> = {
      success: true,
      data: workflow,
      message: 'Workflow activated successfully',
    };
    return NextResponse.json(response);
  } catch (error: any) {
    console.error('Error activating workflow:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to activate workflow',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const workflowId = params.id;
    
    if (!workflowId) {
      const response: ApiResponse = {
        success: false,
        error: 'Workflow ID is required',
      };
      return NextResponse.json(response, { status: 400 });
    }

    const workflow = await n8nClient.deactivateWorkflow(workflowId);
    
    const response: ApiResponse<N8nWorkflow> = {
      success: true,
      data: workflow,
      message: 'Workflow deactivated successfully',
    };
    return NextResponse.json(response);
  } catch (error: any) {
    console.error('Error deactivating workflow:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to deactivate workflow',
    };
    return NextResponse.json(response, { status: 500 });
  }
}
