# Authentication Setup Guide

This guide will help you set up user authentication for the n8n Service Integration Hub using Supabase Auth.

## 🚀 Quick Start

### 1. Database Setup

**Option A: Run the complete schema (recommended)**
Execute the contents of `supabase-schema-fixed.sql` in your Supabase SQL editor.

**Option B: Step-by-step setup (if you encounter issues)**
If you get foreign key constraint errors, use `supabase-setup-steps.sql` and run each step individually.

**What this creates:**

- `users` table for extended user profiles
- Updated `service_connections` table with required user_id
- Updated `connection_logs` table with user tracking
- Row Level Security (RLS) policies for multi-tenant isolation
- Automatic user profile creation trigger
- Sample service templates for quick setup

### 2. Supabase Auth Configuration

In your Supabase dashboard:

1. **Go to Authentication > Settings**
2. **Enable Email Authentication**
3. **Configure Email Templates** (optional)
4. **Set Site URL**: `http://localhost:3000` (development)
5. **Add Redirect URLs**: `http://localhost:3000/**`

### 3. Environment Variables

Your `.env.local` file should already contain:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Security
ENCRYPTION_KEY=your-encryption-key
```

## 🔐 Authentication Features

### User Registration & Login

- Email/password authentication
- Automatic user profile creation
- Secure session management
- Password validation and security

### Protected Routes

- All main application routes require authentication
- Automatic redirect to login for unauthenticated users
- Loading states and error handling

### User Profile Management

- Display name and avatar customization
- Profile update functionality
- Account information display

### Multi-Tenant Data Isolation

- Row Level Security (RLS) policies
- User-scoped service connections
- Audit logging with user tracking

## 🛠️ Technical Implementation

### Authentication Context

- React Context for global auth state
- Automatic session persistence
- Real-time auth state changes

### Database Security

- All sensitive data encrypted before storage
- RLS policies ensure data isolation
- Audit trails for all user actions

### API Security

- User context validation in all API routes
- Secure credential handling
- Protected endpoints

## 📱 User Interface

### Authentication Components

- **AuthForm**: Login/signup form with validation
- **UserMenu**: Profile dropdown with user actions
- **ProtectedRoute**: Route protection wrapper

### User Experience

- Seamless authentication flow
- Loading states and error handling
- Responsive design for all devices

## 🔧 Development Workflow

### Testing Authentication

1. **Start the development server**:

   ```bash
   npm run dev
   ```

2. **Visit http://localhost:3000**
3. **Create a new account** or sign in
4. **Test protected routes** and user-specific data

### Database Verification

Check that RLS is working:

```sql
-- This should only return the current user's connections
SELECT * FROM service_connections;

-- This should only return the current user's logs
SELECT * FROM connection_logs;
```

## 🚨 Security Considerations

### Production Deployment

1. **Update Site URLs** in Supabase Auth settings
2. **Configure proper CORS** settings
3. **Use HTTPS** for all endpoints
4. **Rotate encryption keys** regularly

### Best Practices

- Never expose service role keys in client-side code
- Validate user permissions on all API endpoints
- Use encrypted storage for sensitive credentials
- Implement proper error handling without exposing sensitive data

## 🔄 Migration from Single-User

If you have existing data:

1. **Backup your current data**
2. **Run the new schema** (it will preserve existing data)
3. **Assign existing connections** to a user:
   ```sql
   UPDATE service_connections
   SET user_id = 'your-user-id'
   WHERE user_id IS NULL;
   ```

## 📊 Monitoring & Analytics

### User Activity

- Connection creation/deletion tracking
- Error logging with user context
- Session management and monitoring

### Database Performance

- Indexed queries for user-scoped data
- Optimized RLS policies
- Connection pooling for scalability

## 🆘 Troubleshooting

### Common Issues

1. **"User ID is required" errors**

   - Ensure user is authenticated before API calls
   - Check that userId is passed in API requests

2. **RLS policy errors**

   - Verify user is authenticated
   - Check that policies are correctly applied

3. **Profile not created**
   - Check the `handle_new_user()` trigger
   - Verify user registration completed successfully

### Debug Mode

Enable debug logging in `.env.local`:

```env
DEBUG=true
NODE_ENV=development
```

## 🎯 Next Steps

1. **Test the authentication flow**
2. **Create your first authenticated service connection**
3. **Explore user profile and settings pages**
4. **Set up n8n integration** with user-specific workflows

The authentication system is now fully integrated and ready for multi-tenant use!
