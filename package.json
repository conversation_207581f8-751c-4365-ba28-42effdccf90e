{"name": "n8n-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "n8n:start": "docker-compose up -d", "n8n:stop": "docker-compose down", "n8n:logs": "docker-compose logs -f n8n"}, "dependencies": {"@hookform/resolvers": "^3.3.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.50.0", "axios": "^1.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "lucide-react": "^0.460.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.48.0", "tailwind-merge": "^2.0.0", "zod": "^3.22.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}