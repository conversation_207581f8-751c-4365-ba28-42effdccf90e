# Quick Start Guide

Get your n8n + Next.js automation system up and running in 5 minutes!

## 🚀 Quick Setup

### 1. Install Dependencies
```bash
npm install --legacy-peer-deps
```

### 2. Start n8n Backend
```bash
npm run n8n:start
```
Wait for services to start (check with `npm run n8n:logs`)

### 3. Start Frontend
```bash
npm run dev
```

### 4. Access Applications
- **Frontend**: http://localhost:3000
- **n8n**: http://localhost:5678 (admin/admin123)

## 🔧 First Time Setup

### Configure n8n API Key
1. Open n8n at http://localhost:5678
2. Login with `admin` / `admin123`
3. Go to Settings → API Keys
4. Generate new API key
5. Update `.env.local` with the key:
```env
N8N_API_KEY=your-generated-api-key
```

### Test the Integration
1. Go to http://localhost:3000
2. Navigate to "Connections"
3. Try connecting a service (Telegram recommended for testing)

## 📱 Connect Telegram Bot (Easiest Test)

### Get Bot Token
1. Open Telegram
2. Search for `@BotFather`
3. Send `/newbot`
4. Follow instructions
5. Copy the bot token

### Connect in App
1. Go to Connections → Connect Telegram
2. Enter bot token and name
3. Click "Test Connection"
4. If successful, click "Create Connection"

### Test Webhook
1. Send a message to your bot
2. Check n8n executions at http://localhost:5678
3. View processed data in the workflow

## 🛠️ Useful Commands

```bash
# View n8n logs
npm run n8n:logs

# Stop n8n
npm run n8n:stop

# Restart n8n
npm run n8n:stop && npm run n8n:start

# Reset database (if needed)
docker-compose down -v && npm run n8n:start
```

## 🐛 Common Issues

**"Connection failed" errors**
- Check if n8n is running: `docker-compose ps`
- Verify API key is set correctly
- Check n8n logs: `npm run n8n:logs`

**Webhook not receiving data**
- Ensure workflow is active in n8n
- Check webhook URL is accessible
- Verify external service configuration

**Database connection issues**
- Reset database: `docker-compose down -v`
- Restart services: `npm run n8n:start`

## 📚 Next Steps

1. **Explore Templates**: Browse workflow templates in the app
2. **Add More Services**: Connect WhatsApp, Email, Slack
3. **Create Workflows**: Build custom automation workflows
4. **Monitor Executions**: Track workflow performance
5. **Read Documentation**: Check the full README.md

## 🎯 Example Use Cases

- **Customer Support**: Auto-respond to Telegram/WhatsApp messages
- **Order Notifications**: Send updates via multiple channels
- **Lead Management**: Capture and route leads automatically
- **Content Distribution**: Share content across platforms
- **Monitoring Alerts**: Get notified of system events

Happy automating! 🚀
