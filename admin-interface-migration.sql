-- ============================================================================
-- ADMIN INTERFACE MIGRATION - Additional Tables and Policies
-- ============================================================================
-- This script adds the additional tables needed for the admin interface
-- Run this AFTER the main SaaS migration is complete
-- ============================================================================

-- ============================================================================
-- STEP 1: CREATE ADMIN-SPECIFIC TABLES
-- ============================================================================

-- Admin activity logs for audit trail
CREATE TABLE IF NOT EXISTS public.admin_activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100),
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System settings for platform configuration
CREATE TABLE IF NOT EXISTS public.system_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key VARCHAR(100) NOT NULL UNIQUE,
    value JSONB NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    category VARCHAR(50) DEFAULT 'general',
    updated_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Feature flags for A/B testing and gradual rollouts
CREATE TABLE IF NOT EXISTS public.feature_flags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_enabled BOOLEAN DEFAULT false,
    target_percentage INTEGER DEFAULT 100 CHECK (target_percentage >= 0 AND target_percentage <= 100),
    target_user_segments JSONB DEFAULT '[]',
    conditions JSONB DEFAULT '{}',
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content management for marketing pages
CREATE TABLE IF NOT EXISTS public.content_pages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    slug VARCHAR(100) NOT NULL UNIQUE,
    title VARCHAR(200) NOT NULL,
    content JSONB NOT NULL,
    meta_title VARCHAR(200),
    meta_description TEXT,
    is_published BOOLEAN DEFAULT false,
    published_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Blog posts management
CREATE TABLE IF NOT EXISTS public.blog_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) NOT NULL UNIQUE,
    excerpt TEXT,
    content TEXT NOT NULL,
    featured_image TEXT,
    tags TEXT[] DEFAULT '{}',
    category VARCHAR(50),
    is_published BOOLEAN DEFAULT false,
    published_at TIMESTAMP WITH TIME ZONE,
    view_count INTEGER DEFAULT 0,
    author_id UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email templates for automated communications
CREATE TABLE IF NOT EXISTS public.email_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    subject VARCHAR(200) NOT NULL,
    html_content TEXT NOT NULL,
    text_content TEXT,
    template_variables JSONB DEFAULT '[]',
    category VARCHAR(50) DEFAULT 'transactional',
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Support tickets for customer service
CREATE TABLE IF NOT EXISTS public.support_tickets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ticket_number VARCHAR(20) NOT NULL UNIQUE,
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    subject VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'waiting', 'resolved', 'closed')),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    category VARCHAR(50),
    assigned_to UUID REFERENCES public.users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Support ticket messages/replies
CREATE TABLE IF NOT EXISTS public.support_ticket_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ticket_id UUID NOT NULL REFERENCES public.support_tickets(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    message TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT false,
    attachments JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics events for tracking user behavior
CREATE TABLE IF NOT EXISTS public.analytics_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    session_id VARCHAR(100),
    event_name VARCHAR(100) NOT NULL,
    event_properties JSONB DEFAULT '{}',
    page_url TEXT,
    referrer TEXT,
    user_agent TEXT,
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 2: CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Admin activity logs indexes
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_admin_user_id ON public.admin_activity_logs(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_action ON public.admin_activity_logs(action);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_resource_type ON public.admin_activity_logs(resource_type);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_created_at ON public.admin_activity_logs(created_at);

-- System settings indexes
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON public.system_settings(key);
CREATE INDEX IF NOT EXISTS idx_system_settings_category ON public.system_settings(category);
CREATE INDEX IF NOT EXISTS idx_system_settings_is_public ON public.system_settings(is_public);

-- Feature flags indexes
CREATE INDEX IF NOT EXISTS idx_feature_flags_name ON public.feature_flags(name);
CREATE INDEX IF NOT EXISTS idx_feature_flags_is_enabled ON public.feature_flags(is_enabled);

-- Content pages indexes
CREATE INDEX IF NOT EXISTS idx_content_pages_slug ON public.content_pages(slug);
CREATE INDEX IF NOT EXISTS idx_content_pages_is_published ON public.content_pages(is_published);

-- Blog posts indexes
CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON public.blog_posts(slug);
CREATE INDEX IF NOT EXISTS idx_blog_posts_is_published ON public.blog_posts(is_published);
CREATE INDEX IF NOT EXISTS idx_blog_posts_published_at ON public.blog_posts(published_at);
CREATE INDEX IF NOT EXISTS idx_blog_posts_category ON public.blog_posts(category);
CREATE INDEX IF NOT EXISTS idx_blog_posts_author_id ON public.blog_posts(author_id);

-- Email templates indexes
CREATE INDEX IF NOT EXISTS idx_email_templates_slug ON public.email_templates(slug);
CREATE INDEX IF NOT EXISTS idx_email_templates_category ON public.email_templates(category);
CREATE INDEX IF NOT EXISTS idx_email_templates_is_active ON public.email_templates(is_active);

-- Support tickets indexes
CREATE INDEX IF NOT EXISTS idx_support_tickets_ticket_number ON public.support_tickets(ticket_number);
CREATE INDEX IF NOT EXISTS idx_support_tickets_user_id ON public.support_tickets(user_id);
CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON public.support_tickets(status);
CREATE INDEX IF NOT EXISTS idx_support_tickets_assigned_to ON public.support_tickets(assigned_to);
CREATE INDEX IF NOT EXISTS idx_support_tickets_created_at ON public.support_tickets(created_at);

-- Support ticket messages indexes
CREATE INDEX IF NOT EXISTS idx_support_ticket_messages_ticket_id ON public.support_ticket_messages(ticket_id);
CREATE INDEX IF NOT EXISTS idx_support_ticket_messages_user_id ON public.support_ticket_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_support_ticket_messages_created_at ON public.support_ticket_messages(created_at);

-- Analytics events indexes
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON public.analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_session_id ON public.analytics_events(session_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_name ON public.analytics_events(event_name);
CREATE INDEX IF NOT EXISTS idx_analytics_events_created_at ON public.analytics_events(created_at);

-- ============================================================================
-- STEP 3: CREATE UPDATED_AT TRIGGERS
-- ============================================================================

-- System settings trigger
DROP TRIGGER IF EXISTS update_system_settings_updated_at ON public.system_settings;
CREATE TRIGGER update_system_settings_updated_at 
    BEFORE UPDATE ON public.system_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Feature flags trigger
DROP TRIGGER IF EXISTS update_feature_flags_updated_at ON public.feature_flags;
CREATE TRIGGER update_feature_flags_updated_at 
    BEFORE UPDATE ON public.feature_flags 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Content pages trigger
DROP TRIGGER IF EXISTS update_content_pages_updated_at ON public.content_pages;
CREATE TRIGGER update_content_pages_updated_at 
    BEFORE UPDATE ON public.content_pages 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Blog posts trigger
DROP TRIGGER IF EXISTS update_blog_posts_updated_at ON public.blog_posts;
CREATE TRIGGER update_blog_posts_updated_at 
    BEFORE UPDATE ON public.blog_posts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Email templates trigger
DROP TRIGGER IF EXISTS update_email_templates_updated_at ON public.email_templates;
CREATE TRIGGER update_email_templates_updated_at 
    BEFORE UPDATE ON public.email_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Support tickets trigger
DROP TRIGGER IF EXISTS update_support_tickets_updated_at ON public.support_tickets;
CREATE TRIGGER update_support_tickets_updated_at 
    BEFORE UPDATE ON public.support_tickets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- STEP 4: ENABLE ROW LEVEL SECURITY
-- ============================================================================

-- Enable RLS on all new tables
ALTER TABLE public.admin_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_ticket_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics_events ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 5: CREATE RLS POLICIES
-- ============================================================================

-- Admin activity logs policies (admin-only access)
CREATE POLICY "Admins can view all activity logs" ON public.admin_activity_logs
    FOR SELECT USING (
        auth.uid() IN (SELECT id FROM public.users WHERE is_admin = true)
    );

CREATE POLICY "Service role can insert activity logs" ON public.admin_activity_logs
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- System settings policies
CREATE POLICY "Admins can manage system settings" ON public.system_settings
    FOR ALL USING (
        auth.uid() IN (SELECT id FROM public.users WHERE is_admin = true)
    );

CREATE POLICY "Anyone can view public settings" ON public.system_settings
    FOR SELECT USING (is_public = true);

-- Feature flags policies
CREATE POLICY "Admins can manage feature flags" ON public.feature_flags
    FOR ALL USING (
        auth.uid() IN (SELECT id FROM public.users WHERE is_admin = true)
    );

-- Content pages policies
CREATE POLICY "Anyone can view published content pages" ON public.content_pages
    FOR SELECT USING (is_published = true);

CREATE POLICY "Admins can manage content pages" ON public.content_pages
    FOR ALL USING (
        auth.uid() IN (SELECT id FROM public.users WHERE is_admin = true)
    );

-- Blog posts policies
CREATE POLICY "Anyone can view published blog posts" ON public.blog_posts
    FOR SELECT USING (is_published = true);

CREATE POLICY "Admins can manage blog posts" ON public.blog_posts
    FOR ALL USING (
        auth.uid() IN (SELECT id FROM public.users WHERE is_admin = true)
    );

-- Email templates policies (admin-only)
CREATE POLICY "Admins can manage email templates" ON public.email_templates
    FOR ALL USING (
        auth.uid() IN (SELECT id FROM public.users WHERE is_admin = true)
    );

-- Support tickets policies
CREATE POLICY "Users can view their own support tickets" ON public.support_tickets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create support tickets" ON public.support_tickets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all support tickets" ON public.support_tickets
    FOR ALL USING (
        auth.uid() IN (SELECT id FROM public.users WHERE is_admin = true)
    );

-- Support ticket messages policies
CREATE POLICY "Users can view messages for their tickets" ON public.support_ticket_messages
    FOR SELECT USING (
        ticket_id IN (
            SELECT id FROM public.support_tickets WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can reply to their tickets" ON public.support_ticket_messages
    FOR INSERT WITH CHECK (
        ticket_id IN (
            SELECT id FROM public.support_tickets WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all ticket messages" ON public.support_ticket_messages
    FOR ALL USING (
        auth.uid() IN (SELECT id FROM public.users WHERE is_admin = true)
    );

-- Analytics events policies
CREATE POLICY "Users can view their own analytics events" ON public.analytics_events
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can insert analytics events" ON public.analytics_events
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

CREATE POLICY "Admins can view all analytics events" ON public.analytics_events
    FOR SELECT USING (
        auth.uid() IN (SELECT id FROM public.users WHERE is_admin = true)
    );

-- ============================================================================
-- STEP 6: INSERT INITIAL DATA
-- ============================================================================

-- Insert default system settings
INSERT INTO public.system_settings (key, value, description, is_public, category) VALUES
('platform_name', '"n8n Service Integration Hub"', 'Platform display name', true, 'branding'),
('maintenance_mode', 'false', 'Enable maintenance mode', false, 'system'),
('user_registration_enabled', 'true', 'Allow new user registrations', false, 'auth'),
('max_free_workflows', '3', 'Maximum workflows for free plan', false, 'limits'),
('max_free_connections', '5', 'Maximum connections for free plan', false, 'limits'),
('support_email', '"<EMAIL>"', 'Support contact email', true, 'contact'),
('analytics_enabled', 'true', 'Enable analytics tracking', false, 'analytics')
ON CONFLICT (key) DO NOTHING;

-- Insert default email templates
INSERT INTO public.email_templates (name, slug, subject, html_content, text_content, category) VALUES
(
    'Welcome Email',
    'welcome',
    'Welcome to {{platform_name}}!',
    '<h1>Welcome {{user_name}}!</h1><p>Thank you for joining {{platform_name}}. Get started by exploring our workflow templates.</p>',
    'Welcome {{user_name}}! Thank you for joining {{platform_name}}. Get started by exploring our workflow templates.',
    'onboarding'
),
(
    'Subscription Confirmation',
    'subscription_confirmed',
    'Subscription Confirmed - {{plan_name}}',
    '<h1>Subscription Confirmed</h1><p>Your {{plan_name}} subscription is now active. Enjoy your enhanced features!</p>',
    'Subscription Confirmed. Your {{plan_name}} subscription is now active. Enjoy your enhanced features!',
    'billing'
),
(
    'Password Reset',
    'password_reset',
    'Reset Your Password',
    '<h1>Reset Your Password</h1><p>Click the link below to reset your password: {{reset_link}}</p>',
    'Reset Your Password. Click the link below to reset your password: {{reset_link}}',
    'auth'
)
ON CONFLICT (slug) DO NOTHING;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify all admin tables were created
SELECT 'Admin tables created successfully!' as status;

SELECT table_name, table_type
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN (
    'admin_activity_logs',
    'system_settings',
    'feature_flags',
    'content_pages',
    'blog_posts',
    'email_templates',
    'support_tickets',
    'support_ticket_messages',
    'analytics_events'
)
ORDER BY table_name;

-- Verify RLS is enabled
SELECT
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN (
    'admin_activity_logs',
    'system_settings',
    'feature_flags',
    'content_pages',
    'blog_posts',
    'email_templates',
    'support_tickets',
    'support_ticket_messages',
    'analytics_events'
)
ORDER BY tablename;

-- Verify initial data was inserted
SELECT 'System settings inserted:' as info, COUNT(*) as count FROM public.system_settings;
SELECT 'Email templates inserted:' as info, COUNT(*) as count FROM public.email_templates;

SELECT 'Admin interface migration completed successfully!' as final_status;
