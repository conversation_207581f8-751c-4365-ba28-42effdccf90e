import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { getDatabaseService } from "@/lib/database-service";
import { ApiResponse } from "@/types/n8n";
import { ServiceConnection } from "@/types/database";
import { Database } from "@/types/database";

const databaseService = getDatabaseService();

// Helper function to get authenticated user from request
async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  if (!authHeader?.startsWith("Bearer ")) {
    return null;
  }

  const token = authHeader.substring(7);
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

  const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);

  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(token);
    return error ? null : user;
  } catch {
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    // For now, we'll use userId from query params for development
    // In production, you should use the authenticated user from JWT
    const { searchParams } = new URL(request.url);
    const connectionId = searchParams.get("id");
    const userId = searchParams.get("userId");

    if (!userId) {
      const response: ApiResponse = {
        success: false,
        error: "User ID is required",
      };
      return NextResponse.json(response, { status: 401 });
    }

    if (connectionId) {
      // Get specific connection
      const connection = await databaseService.getServiceConnection(
        connectionId
      );

      if (!connection || connection.user_id !== userId) {
        const response: ApiResponse = {
          success: false,
          error: "Connection not found",
        };
        return NextResponse.json(response, { status: 404 });
      }

      const response: ApiResponse<ServiceConnection> = {
        success: true,
        data: connection,
      };
      return NextResponse.json(response);
    } else {
      // Get all connections for user
      const connections = await databaseService.getServiceConnections(userId);
      const response: ApiResponse<ServiceConnection[]> = {
        success: true,
        data: connections,
      };
      return NextResponse.json(response);
    }
  } catch (error: any) {
    console.error("Error fetching connections:", error);
    const response: ApiResponse = {
      success: false,
      error: error.message || "Failed to fetch connections",
    };
    return NextResponse.json(response, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const connectionData = await request.json();

    // Ensure user_id is provided
    if (!connectionData.user_id) {
      const response: ApiResponse = {
        success: false,
        error: "User ID is required",
      };
      return NextResponse.json(response, { status: 401 });
    }

    const connection = await databaseService.createServiceConnection(
      connectionData
    );
    const response: ApiResponse<ServiceConnection> = {
      success: true,
      data: connection,
      message: "Connection created successfully",
    };
    return NextResponse.json(response, { status: 201 });
  } catch (error: any) {
    console.error("Error creating connection:", error);
    const response: ApiResponse = {
      success: false,
      error: error.message || "Failed to create connection",
    };
    return NextResponse.json(response, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const connectionId = searchParams.get("id");

    if (!connectionId) {
      const response: ApiResponse = {
        success: false,
        error: "Connection ID is required",
      };
      return NextResponse.json(response, { status: 400 });
    }

    const updateData = await request.json();
    const connection = await databaseService.updateServiceConnection(
      connectionId,
      updateData
    );

    const response: ApiResponse<ServiceConnection> = {
      success: true,
      data: connection,
      message: "Connection updated successfully",
    };
    return NextResponse.json(response);
  } catch (error: any) {
    console.error("Error updating connection:", error);
    const response: ApiResponse = {
      success: false,
      error: error.message || "Failed to update connection",
    };
    return NextResponse.json(response, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const connectionId = searchParams.get("id");
    const userId = searchParams.get("userId");

    if (!connectionId) {
      const response: ApiResponse = {
        success: false,
        error: "Connection ID is required",
      };
      return NextResponse.json(response, { status: 400 });
    }

    if (!userId) {
      const response: ApiResponse = {
        success: false,
        error: "User ID is required",
      };
      return NextResponse.json(response, { status: 401 });
    }

    await databaseService.deleteServiceConnection(connectionId, userId);

    const response: ApiResponse = {
      success: true,
      message: "Connection deleted successfully",
    };
    return NextResponse.json(response);
  } catch (error: any) {
    console.error("Error deleting connection:", error);
    const response: ApiResponse = {
      success: false,
      error: error.message || "Failed to delete connection",
    };
    return NextResponse.json(response, { status: 500 });
  }
}
