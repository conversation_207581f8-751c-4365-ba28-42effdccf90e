"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { useAuth } from "@/lib/auth-context";
import {
  Activity,
  Workflow,
  Zap,
  Settings,
  Plus,
  CheckCircle,
  AlertCircle,
  Clock,
} from "lucide-react";

interface DashboardStats {
  totalWorkflows: number;
  activeWorkflows: number;
  totalExecutions: number;
  connectedServices: number;
}

function DashboardContent() {
  const { user, userProfile } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalWorkflows: 0,
    activeWorkflows: 0,
    totalExecutions: 0,
    connectedServices: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load user-specific dashboard data
    const loadDashboardData = async () => {
      if (!user) return;

      try {
        // Fetch user-specific connections
        const connectionsResponse = await fetch(
          `/api/connections?userId=${user.id}`
        );
        const connectionsResult = await connectionsResponse.json();

        const connectedServices = connectionsResult.success
          ? connectionsResult.data.length
          : 0;

        // In a real app, this would fetch user-specific workflow data from n8n
        await new Promise((resolve) => setTimeout(resolve, 1000));
        setStats({
          totalWorkflows: 12,
          activeWorkflows: 8,
          totalExecutions: 1247,
          connectedServices,
        });
      } catch (error) {
        console.error("Failed to load dashboard data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();
  }, [user]);

  const recentActivity = [
    {
      id: 1,
      type: "workflow_executed",
      title: "Telegram Bot Response",
      description: "Workflow executed successfully",
      timestamp: "2 minutes ago",
      status: "success",
    },
    {
      id: 2,
      type: "service_connected",
      title: "WhatsApp Connected",
      description: "New service connection established",
      timestamp: "1 hour ago",
      status: "success",
    },
    {
      id: 3,
      type: "workflow_error",
      title: "Email Notification Failed",
      description: "Workflow execution failed",
      timestamp: "3 hours ago",
      status: "error",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Content Management
              </h1>
              <p className="text-gray-600 mt-1">
                Manage your service connections with ease
              </p>
            </div>
            <Link href="/connections">
              <button className="btn-primary flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>New Connection</span>
              </button>
            </Link>
          </div>
        </div>

        {/* Welcome Message */}
        <div className="content-card mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
              <Activity className="w-5 h-5 text-gray-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900">
              Connection Overview
            </h2>
          </div>
          <p className="text-gray-600 mb-6">
            Welcome back,{" "}
            {userProfile?.display_name || user?.email?.split("@")[0] || "User"}!
            Manage your service connections organized by status.
          </p>
        </div>
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="stat-card stat-card-blue">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700 mb-1">
                  Total Workflows
                </p>
                <p className="text-3xl font-bold text-blue-900">
                  {isLoading ? "..." : stats.totalWorkflows}
                </p>
              </div>
              <Workflow className="w-8 h-8 text-blue-600" />
            </div>
          </div>

          <div className="stat-card stat-card-green">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-700 mb-1">
                  Active
                </p>
                <p className="text-3xl font-bold text-green-900">
                  {isLoading ? "..." : stats.activeWorkflows}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </div>

          <div className="stat-card stat-card-orange">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700 mb-1">
                  Executions
                </p>
                <p className="text-3xl font-bold text-orange-900">
                  {isLoading ? "..." : stats.totalExecutions}
                </p>
              </div>
              <Zap className="w-8 h-8 text-orange-600" />
            </div>
          </div>

          <div className="stat-card stat-card-purple">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700 mb-1">
                  Connected
                </p>
                <p className="text-3xl font-bold text-purple-900">
                  {isLoading ? "..." : stats.connectedServices}
                </p>
              </div>
              <Settings className="w-8 h-8 text-purple-600" />
            </div>
          </div>
        </div>

        {/* Connected Services Section */}
        <div className="content-card">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-6 h-6 bg-yellow-100 rounded flex items-center justify-center">
              <span className="text-yellow-600 text-sm">⚡</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">
              Connected Services ({stats.connectedServices})
            </h3>
          </div>
          <p className="text-gray-600 mb-6">
            Live services visible to workflows
          </p>

          <div className="space-y-4">
            {/* Service Connection Items */}
            {recentActivity.map((activity) => (
              <div
                key={activity.id}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    {activity.status === "success" && (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    )}
                    {activity.status === "error" && (
                      <AlertCircle className="w-5 h-5 text-red-600" />
                    )}
                    {activity.status === "pending" && (
                      <Clock className="w-5 h-5 text-orange-600" />
                    )}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">
                      {activity.title}
                    </h4>
                    <p className="text-sm text-gray-500">
                      {activity.description} • {activity.timestamp}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      activity.status === "success"
                        ? "bg-green-100 text-green-800"
                        : activity.status === "error"
                        ? "bg-red-100 text-red-800"
                        : "bg-orange-100 text-orange-800"
                    }`}
                  >
                    {activity.status === "success"
                      ? "Connected"
                      : activity.status === "error"
                      ? "Error"
                      : "Pending"}
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {activity.type.replace("_", " ")}
                  </span>
                </div>
              </div>
            ))}

            <div className="flex items-center justify-center py-8">
              <Link
                href="/connections"
                className="text-blue-600 hover:text-blue-700 font-medium"
              >
                View all connections →
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function Dashboard() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
}
