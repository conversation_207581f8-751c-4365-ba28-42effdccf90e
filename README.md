# n8n Service Integration Hub

A secure service integration system that connects external tools like Telegram and WhatsApp to existing n8n workflows. This application provides a user-friendly interface for managing service connections with encrypted credential storage via Supabase.

## 🚀 Features

- **🔐 User Authentication**: Secure multi-tenant authentication with Supabase Auth
- **🔌 Service Integration**: Connect Telegram, WhatsApp, Email, and Slack to n8n workflows
- **🛡️ Secure Credential Storage**: Encrypted storage of API keys and tokens with user isolation
- **🔗 Webhook Management**: Automatic webhook creation and management for external services
- **⚡ Real-time Testing**: Test service connections before deploying to production
- **📊 Audit Logging**: Complete audit trail of all user activities and connections
- **👤 User Management**: Profile management, settings, and user-specific dashboards
- **🔒 Row Level Security**: Database-level security ensuring users only access their own data
- **🎨 Modern UI**: Built with Next.js 15, TypeScript, Tailwind CSS, and Radix UI
- **🐳 Docker Integration**: Easy deployment with Docker Compose

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App  │    │   Supabase DB   │    │   n8n Instance  │    │ External APIs   │
│   (Frontend)    │◄──►│   (Secure)      │    │   (Workflows)   │◄──►│ (Telegram, etc) │
│                 │    │                 │    │                 │    │                 │
│ - Dashboard     │    │ - Encrypted     │    │ - Existing      │    │ - Webhooks      │
│ - Connections   │    │   Credentials   │    │   Workflows     │    │ - API Calls     │
│ - Service Mgmt  │    │ - RLS Policies  │    │ - Executions    │    │ - Integrations  │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Prerequisites

- Node.js 18+ and npm
- Docker and Docker Compose (for n8n)
- Supabase account (free tier available)
- Git

## 🛠️ Installation & Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd n8n-webapp
```

### 2. Install Dependencies

```bash
npm install --legacy-peer-deps
```

### 3. Set Up Supabase Database

Follow the detailed [Supabase Setup Guide](./SUPABASE_SETUP.md) to:

1. Create a Supabase project
2. Set up the database schema with authentication
3. Configure Row Level Security policies
4. Enable Supabase Auth
5. Get your API keys

### 4. Configure Authentication

Follow the [Authentication Setup Guide](./AUTHENTICATION_SETUP.md) to:

1. Set up user authentication
2. Configure Supabase Auth settings
3. Test the authentication flow

### 5. Environment Configuration

Copy the environment template and configure your settings:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:

```env
# n8n Configuration
N8N_HOST=http://localhost:5678
N8N_API_KEY=your-api-key-here
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin123

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here
```

### 6. Start n8n Backend

Start the n8n instance with PostgreSQL database:

```bash
npm run n8n:start
```

This will start:

- PostgreSQL database on port 5432
- n8n instance on port 5678

Wait for the services to be ready (check with `npm run n8n:logs`).

### 7. Configure n8n API Key

1. Open n8n at http://localhost:5678
2. Login with admin/admin123 (or your configured credentials)
3. Go to Settings → API Keys
4. Generate a new API key
5. Update your `.env.local` file with the API key

### 8. Start the Frontend

```bash
npm run dev
```

The application will be available at http://localhost:3000

## 📖 Usage Guide

### Dashboard Overview

The main dashboard provides:

- **Connection Statistics**: Total and active service connections
- **Quick Actions**: Direct links to connect services and manage connections
- **Recent Activity**: Latest connection tests and service integrations
- **Getting Started**: Helpful links for new users

### Connecting External Services

**Important**: This system connects to existing n8n workflows. Create your workflows in n8n first, then use this interface to connect external services as triggers.

#### Telegram Bot Setup

1. Navigate to **Connections** → **Connect Telegram**
2. Get a bot token from @BotFather on Telegram:
   - Send `/newbot` to @BotFather
   - Follow instructions to create your bot
   - Copy the provided token
3. Enter the bot token and a friendly name
4. Test the connection
5. Create the connection (credentials stored encrypted in Supabase)
6. The system automatically creates a webhook workflow in n8n

#### WhatsApp Business Setup

1. Navigate to **Connections** → **Connect WhatsApp**
2. Set up WhatsApp Business API:
   - Go to Facebook Developer Console
   - Create/select an app
   - Add WhatsApp Business API product
   - Get access token and phone number ID
3. Enter your credentials
4. Test the connection
5. Create the connection (credentials stored encrypted in Supabase)
6. The system automatically creates a webhook workflow in n8n

### Connection Management

- **View Connections**: See all service connections with status indicators
- **Test Connections**: Verify service credentials and connectivity
- **Manage Credentials**: Securely update API keys and tokens
- **Monitor Status**: Track connection health and error messages
- **View Workflows**: See existing n8n workflows (read-only)

## 🔧 API Endpoints

### Connection Management

- `GET /api/connections` - List all service connections
- `POST /api/connections` - Create new connection
- `PATCH /api/connections?id={id}` - Update connection
- `DELETE /api/connections?id={id}` - Delete connection

### Service Templates

- `GET /api/service-templates` - List available service templates

### Workflow Integration (Read-Only)

- `GET /api/n8n/workflows` - List existing n8n workflows
- `POST /api/n8n/workflows/{id}/activate` - Activate workflow
- `DELETE /api/n8n/workflows/{id}/activate` - Deactivate workflow

### External Services

- `POST /api/external-services/telegram` - Telegram operations
- `POST /api/external-services/whatsapp` - WhatsApp operations

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   │   ├── connections/   # Connection management APIs
│   │   ├── service-templates/ # Service template APIs
│   │   ├── n8n/          # n8n integration APIs (read-only)
│   │   └── external-services/ # External service APIs
│   ├── connections/       # Service connection pages
│   └── workflows/         # Workflow viewing pages (read-only)
├── components/            # React components
│   ├── ui/               # Base UI components
│   └── external-services/ # Service connection components
├── lib/                   # Utility libraries
│   ├── supabase.ts       # Supabase client configuration
│   ├── database-service.ts # Database operations
│   ├── encryption.ts     # Credential encryption utilities
│   ├── n8n-client.ts    # n8n API client (read-only)
│   ├── webhook-manager.ts # Webhook utilities
│   └── utils.ts          # General utilities
└── types/                 # TypeScript definitions
    ├── database.ts       # Supabase database types
    └── n8n.ts            # n8n-related types
```

## 🔍 Development Commands

```bash
# Start development server
npm run dev

# Start n8n backend
npm run n8n:start

# Stop n8n backend
npm run n8n:stop

# View n8n logs
npm run n8n:logs

# Build for production
npm run build

# Start production server
npm run start

# Lint code
npm run lint
```

## 🔧 Configuration

### n8n Configuration

The Docker Compose setup includes:

- PostgreSQL database for data persistence
- n8n with API access enabled
- Basic authentication configured
- Webhook support enabled

Key environment variables for n8n:

```env
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http
N8N_BASIC_AUTH_ACTIVE=true
N8N_API_KEY=your-api-key-here
```

### Frontend Configuration

Environment variables for the Next.js app:

```env
# Required - n8n Integration
N8N_HOST=http://localhost:5678
N8N_API_KEY=your-api-key-here

# Required - Supabase Database
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Required - Security
ENCRYPTION_KEY=your-32-character-encryption-key
```

### Supabase Configuration

The application uses Supabase for secure credential storage with:

- **Row Level Security (RLS)**: User-isolated data access
- **Encrypted Storage**: All sensitive credentials encrypted before storage
- **Audit Logging**: Connection actions tracked for security
- **Service Templates**: Predefined service configurations

## 🚀 Deployment

### Production Deployment

1. **Build the application**:

```bash
npm run build
```

2. **Configure production environment**:

```env
NODE_ENV=production
N8N_HOST=https://your-n8n-instance.com
N8N_API_KEY=your-production-api-key
```

3. **Deploy with Docker** (optional):

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### Cloud Deployment Options

- **Vercel**: Deploy the frontend to Vercel
- **Railway**: Deploy both n8n and frontend
- **DigitalOcean**: Use App Platform or Droplets
- **AWS**: Use ECS, Lambda, or EC2

## 🧪 Testing

### Connection Testing

The application includes built-in connection testing:

- **Telegram**: Validates bot token with Telegram API
- **WhatsApp**: Verifies access token and phone number ID
- **Email**: Tests SMTP connection
- **Slack**: Validates webhook URL

### Manual Testing

1. Start the development environment
2. Navigate to Connections page
3. Select a service to connect
4. Enter test credentials
5. Use "Test Connection" button
6. Verify the connection status

## 🔐 Security Considerations

### Credential Storage (Supabase)

- All sensitive credentials encrypted with AES before database storage
- Encryption keys stored separately from database
- Row Level Security (RLS) ensures user data isolation
- Service role key used only for server-side operations
- Audit logging tracks all credential access

### API Keys and Secrets

- Store sensitive data in environment variables
- Use strong, unique encryption keys (32+ characters)
- Rotate credentials regularly
- Never commit secrets to version control
- Use Supabase's built-in security features

### Webhook Security

- Use HTTPS in production
- Implement webhook signature verification
- Validate incoming webhook data
- Rate limit webhook endpoints

### n8n Security

- Enable basic authentication
- Use strong passwords
- Restrict API access
- Monitor execution logs

## 🐛 Troubleshooting

### Common Issues

**n8n Connection Failed**

```bash
# Check if n8n is running
docker-compose ps

# View n8n logs
npm run n8n:logs

# Restart n8n
npm run n8n:stop && npm run n8n:start
```

**API Key Issues**

1. Verify API key is correctly set in n8n
2. Check environment variable is loaded
3. Ensure API key has proper permissions

**Webhook Not Working**

1. Check webhook URL is accessible
2. Verify external service configuration
3. Check n8n workflow is active
4. Review execution logs

**Database Connection Issues**

```bash
# Reset database
docker-compose down -v
docker-compose up -d
```

### Debug Mode

Enable debug logging:

```env
DEBUG=true
N8N_LOG_LEVEL=debug
```

## 📚 Additional Resources

### Documentation

- [n8n Documentation](https://docs.n8n.io/)
- [Next.js Documentation](https://nextjs.org/docs)
- [Telegram Bot API](https://core.telegram.org/bots/api)
- [WhatsApp Business API](https://developers.facebook.com/docs/whatsapp)

### Community

- [n8n Community Forum](https://community.n8n.io/)
- [n8n Discord](https://discord.gg/n8n)
- [GitHub Issues](https://github.com/n8n-io/n8n/issues)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [n8n](https://n8n.io/) for the powerful automation platform
- [Next.js](https://nextjs.org/) for the React framework
- [Supabase](https://supabase.com/) for the secure database and authentication
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [Radix UI](https://www.radix-ui.com/) for accessible components
